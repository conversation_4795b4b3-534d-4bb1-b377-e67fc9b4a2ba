<?xml version="1.0" encoding="UTF-8"?>
<InstallationLog>
	<ProductName>FIS_2</ProductName>
	<FeatureName>fisprod</FeatureName>
	<Creator>Installed by InstallAnywhere 17.0 Premier Build 5158</Creator>
	<InstallationDirectory>C:\Program Files (x86)\fis2</InstallationDirectory>
	<StartupInfo>
		<FreeMemory>21311 kB</FreeMemory>
		<TotalMemory>33680 kB</TotalMemory>
		<CommandLineArgs/>
		<StartupPath name="java.class.path">
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData\IAClasses.zip</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData\Execute.zip</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\Windows\InstallerData\Execute.zip</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData\Resource1.zip</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\Windows\InstallerData\Resource1.zip</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\Windows\InstallerData</PathElement>
		</StartupPath>
		<StartupPath name="ZGUtil.CLASS_PATH">
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData\IAClasses.zip</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData\Execute.zip</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData</PathElement>
		</StartupPath>
		<StartupPath name="sun.boot.class.path">
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\resources.jar</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\rt.jar</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\sunrsasign.jar</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\jsse.jar</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\jce.jar</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\charsets.jar</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\jfr.jar</PathElement>
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\classes</PathElement>
		</StartupPath>
		<StartupPath name="java.ext.dirs">
			<PathElement>C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\ext</PathElement>
			<PathElement>C:\WINDOWS\Sun\Java\lib\ext</PathElement>
		</StartupPath>
		<SystemProperty name="java.version">1.8.0_72 (Java 1)</SystemProperty>
		<SystemProperty name="java.vm.name">Java HotSpot(TM) Client VM</SystemProperty>
		<SystemProperty name="java.vm.vendor">Oracle Corporation</SystemProperty>
		<SystemProperty name="java.vm.version">25.72-b15</SystemProperty>
		<SystemProperty name="java.vm.specification.name">Java Virtual Machine Specification</SystemProperty>
		<SystemProperty name="java.vm.specification.vendor">Oracle Corporation</SystemProperty>
		<SystemProperty name="java.vm.specification.version">1.8</SystemProperty>
		<SystemProperty name="java.specfication.name"/>
		<SystemProperty name="java.specification.vendor">Oracle Corporation</SystemProperty>
		<SystemProperty name="java.specification.version">1.8</SystemProperty>
		<SystemProperty name="java.vendor">Oracle Corporation</SystemProperty>
		<SystemProperty name="java.vendor.url">http://java.oracle.com/</SystemProperty>
		<SystemProperty name="java.class.version">52.0</SystemProperty>
		<SystemProperty name="java.compiler"/>
		<SystemProperty name="java.home">C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre</SystemProperty>
		<SystemProperty name="java.io.tmpdir">C:\Users\<USER>\AppData\Local\Temp\</SystemProperty>
		<SystemProperty name="os.name">Windows 10</SystemProperty>
		<SystemProperty name="os.arch">x86</SystemProperty>
		<SystemProperty name="os.version">10.0</SystemProperty>
		<SystemProperty name="path.separator">;</SystemProperty>
		<SystemProperty name="file.separator">\</SystemProperty>
		<SystemProperty name="file.encoding">Cp1252</SystemProperty>
		<SystemProperty name="user.name">Admin</SystemProperty>
		<SystemProperty name="user.home">C:\Users\<USER>\Users\Admin\AppData\Local\Temp\********763\Windows</SystemProperty>
		<SystemProperty name="user.language">en</SystemProperty>
		<SystemProperty name="user.region"/>
	</StartupInfo>
	<UserInteractions>
		<InteractionGroup name="Choose Install Folder">
			<InteractionVariable name="USER_INSTALL_DIR">C:\Program Files (x86)\fis2</InteractionVariable>
		</InteractionGroup>
		<InteractionGroup name="Choose Product Features">
			<InteractionVariable name="CHOSEN_FEATURE_LIST">fisprod</InteractionVariable>
			<InteractionVariable name="CHOSEN_INSTALL_FEATURE_LIST">fisprod</InteractionVariable>
			<InteractionVariable name="CHOSEN_INSTALL_SET">Typical</InteractionVariable>
		</InteractionGroup>
		<InteractionGroup name="Choose a HTML Browser">
			<InteractionVariable name="UserSelectedBrowser">iexplore.exe</InteractionVariable>
			<InteractionVariable name="PathOfSelectedBrowser">C:\Program Files\Internet Explorer\</InteractionVariable>
		</InteractionGroup>
		<InteractionGroup name="Install">
			<InteractionVariable name="-fileOverwrite_C\:\\Program\ Files\ (x86)\\fis2\\UninstallerData\\Uninstall\ fis2.lax">Yes</InteractionVariable>
			<InteractionVariable name="-fileOverwrite_C\:\\Program\ Files\ (x86)\\fis2\\UninstallerData\\resource\\iawin32.dll">Yes</InteractionVariable>
			<InteractionVariable name="-fileOverwrite_C\:\\Program\ Files\ (x86)\\fis2\\UninstallerData\\resource\\win64_32_x64.exe">Yes</InteractionVariable>
			<InteractionVariable name="-fileOverwrite_C\:\\Program\ Files\ (x86)\\fis2\\UninstallerData\\resource\\remove.exe">Yes</InteractionVariable>
			<InteractionVariable name="-fileOverwrite_C\:\\Program\ Files\ (x86)\\fis2\\UninstallerData\\resource\\invoker.exe">Yes</InteractionVariable>
			<InteractionVariable name="-fileOverwrite_C\:\\Program\ Files\ (x86)\\fis2\\hlcl.policy">Yes</InteractionVariable>
			<InteractionVariable name="-fileOverwrite_C\:\\Program\ Files\ (x86)\\fis2\\fisprod.jar">Yes</InteractionVariable>
			<InteractionVariable name="-fileOverwrite_C\:\\Program\ Files\ (x86)\\fis2\\fis2prod.bat">Yes</InteractionVariable>
		</InteractionGroup>
	</UserInteractions>
	<InstallationSummary status="Installation: Successful."/>
	<InstallDuration begin="JULY 31, 2025 10:26:23 AM EAT" end="JULY 31, 2025 10:27:14 AM EAT"/>
	<ActionsSummary>
		<Successes>46</Successes>
		<Warnings>0</Warnings>
		<NonFatalErrors>0</NonFatalErrors>
		<FatalErrors>0</FatalErrors>
		<ActionNotes>None</ActionNotes>
	</ActionsSummary>
	<InstallLogDetails>
		<action name="Check Disk Space" status="successful">
			<destination>C:\Program Files (x86)\fis2</destination>
			<AdditionalNotes>
				<note>Required Disk Space:174,000,176 Bytes Free Disk Space:329,491,415,040 Bytes</note>
			</AdditionalNotes>
		</action>
		<action name="Check Disk Space" status="successful">
			<destination>C:\Program Files (x86)\fis2</destination>
			<AdditionalNotes>
				<note>Required Disk Space:174,000,176 Bytes Free Disk Space:329,491,415,040 Bytes</note>
			</AdditionalNotes>
		</action>
		<action name="Get Registry Entry" status="successful"/>
		<action name="Custom Action" target="com.hlcl.icc.install.InstallHelper" status="successful"/>
		<action name="Check Disk Space" status="successful">
			<destination>C:\Program Files (x86)\fis2</destination>
			<AdditionalNotes>
				<note>Required Disk Space:174,000,176 Bytes Free Disk Space:329,491,410,944 Bytes</note>
			</AdditionalNotes>
		</action>
		<action name="Install Directory" target="java" status="successful">
			<destination>C:\Program Files (x86)\fis2</destination>
		</action>
		<action name="Install JRE" target="java" status="successful">
			<destination>C:\Program Files (x86)\fis2</destination>
		</action>
		<action name="Install Directory" target="cache" status="successful">
			<destination>C:\Users\<USER>\fis2\cache</destination>
		</action>
		<action name="Install Directory" target="prod" status="successful">
			<destination>C:\Users\<USER>\fis2\cache\prod</destination>
		</action>
		<action name="Install Directory" target="fis2" status="successful">
			<destination>C:\Program Files (x86)\fis2</destination>
			<AdditionalNotes>
				<note>Directory already existed</note>
			</AdditionalNotes>
		</action>
		<action name="Install Directory" target="UninstallerData" status="successful">
			<destination>C:\Program Files (x86)\fis2\UninstallerData</destination>
		</action>
		<action name="Install Uninstaller" target="Uninstall fis2.exe" status="successful">
			<destination>C:\Program Files (x86)\fis2\UninstallerData\</destination>
		</action>
		<action name="Install File" target="Uninstall fis2.exe" status="successful">
			<destination>C:\Program Files (x86)\fis2\UninstallerData\</destination>
		</action>
		<action name="Install File" target="Uninstall fis2.lax" status="successful">
			<destination>C:\Program Files (x86)\fis2\UninstallerData\</destination>
		</action>
		<action name="Install Directory" target="resource" status="successful">
			<destination>C:\Program Files (x86)\fis2\UninstallerData\resource</destination>
		</action>
		<action name="Install File" target="iawin32.dll" status="successful">
			<destination>C:\Program Files (x86)\fis2\UninstallerData\resource\</destination>
		</action>
		<action name="Install File" target="win64_32_x64.exe" status="successful">
			<destination>C:\Program Files (x86)\fis2\UninstallerData\resource\</destination>
		</action>
		<action name="Install File" target="remove.exe" status="successful">
			<destination>C:\Program Files (x86)\fis2\UninstallerData\resource\</destination>
		</action>
		<action name="Install File" target="invoker.exe" status="successful">
			<destination>C:\Program Files (x86)\fis2\UninstallerData\resource\</destination>
		</action>
		<action name="Make Registry Entry" target="Value Name: DisplayName, Value: FIS 2" status="successful">
			<destination>HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2</destination>
		</action>
		<action name="Make Registry Entry" target="Value Name: UninstallString, Value: &quot;C:\Program Files (x86)\fis2\UninstallerData\Uninstall fis2.exe&quot;" status="successful">
			<destination>HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2</destination>
		</action>
		<action name="Make Registry Entry" target="Value Name: Publisher, Value: Hapag-Lloyd AG" status="successful">
			<destination>HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2</destination>
		</action>
		<action name="Make Registry Entry" target="Value Name: URLInfoAbout, Value: www.hapag-lloyd.com" status="successful">
			<destination>HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2</destination>
		</action>
		<action name="Make Registry Entry" target="Value Name: URLUpdateInfo, Value: http://fisprod.hlcl.com/fis2installer" status="successful">
			<destination>HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2</destination>
		</action>
		<action name="Make Registry Entry" target="Value Name: Contact, Value: <EMAIL>" status="successful">
			<destination>HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2</destination>
		</action>
		<action name="Make Registry Entry" target="Value Name: ProductID, Value: 1e660ac6-1ec3-11b2-af7f-dae2252e4790" status="successful">
			<destination>HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2</destination>
		</action>
		<action name="Make Registry Entry" target="Value Name: InstallLocation, Value: C:\Program Files (x86)\fis2" status="successful">
			<destination>HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2</destination>
		</action>
		<action name="Make Registry Entry" target="Value Name: InstallDate, Value: Thu Jul 31 10:27:12 EAT 2025" status="successful">
			<destination>HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2</destination>
		</action>
		<action name="Make Registry Entry" target="Value Name: HelpLink, Value: http://fisprod.hlcl.com/fis2pages/main_prereqisites.html" status="successful">
			<destination>HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2</destination>
		</action>
		<action name="Make Registry Entry" target="Value Name: DisplayVersion, Value: 1.0.1.5" status="successful">
			<destination>HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2</destination>
		</action>
		<action name="Make Registry Entry" target="Value Name: Comments, Value: FIS 2 Application Launcher" status="successful">
			<destination>HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2</destination>
		</action>
		<action name="Install File" target="hlcl.policy" status="successful">
			<destination>C:\Program Files (x86)\fis2\</destination>
		</action>
		<action name="Install File" target="fisprod.jar" status="successful">
			<destination>C:\Program Files (x86)\fis2\</destination>
		</action>
		<action name="Install File" target="fis2prod.bat" status="successful">
			<destination>C:\Program Files (x86)\fis2\</destination>
		</action>
		<action name="Modify Text File - Single File" target="fis2prod.bat" status="successful">
			<destination>C:\Program Files (x86)\fis2\</destination>
		</action>
		<action name="Install Directory" target="data" status="successful">
			<destination>C:\Users\<USER>\fis2\data</destination>
		</action>
		<action name="Install Directory" target="data" status="successful">
			<destination>C:\Users\<USER>\fis2\data\data</destination>
		</action>
		<action name="Install Directory" target="FIS 2" status="successful">
			<destination>C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\FIS 2</destination>
		</action>
		<action name="Create Shortcut" target="Uninstall fis2.lnk" status="successful">
			<destination>C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\FIS 2</destination>
		</action>
		<action name="Create Shortcut" target="FIS2.lnk" status="successful">
			<destination>C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\FIS 2</destination>
		</action>
	</InstallLogDetails>
	<STDERR>
	<![CDATA[__________________________________________________________________________

InstallAnywhere 2015
Version: 17.0
__________________________________________________________________________

Thu Jul 31 10:26:20 EAT 2025

Free Memory: 11374 kB
Total Memory: 15872 kB

No Arguments

java.class.path:
    C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData\IAClasses.zip
    C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData\Execute.zip
    C:\Users\<USER>\AppData\Local\Temp\********763\Windows\InstallerData\Execute.zip
    C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData\Resource1.zip
    C:\Users\<USER>\AppData\Local\Temp\********763\Windows\InstallerData\Resource1.zip
    C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData
    C:\Users\<USER>\AppData\Local\Temp\********763\Windows\InstallerData

ZGUtil.CLASS_PATH:
    C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData\IAClasses.zip
    C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData\Execute.zip
    C:\Users\<USER>\AppData\Local\Temp\********763\InstallerData

sun.boot.class.path:
    C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\resources.jar
    C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\rt.jar
    C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\sunrsasign.jar
    C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\jsse.jar
    C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\jce.jar
    C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\charsets.jar
    C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\jfr.jar
    C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\classes

java.ext.dirs:
    C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\lib\ext
    C:\WINDOWS\Sun\Java\lib\ext

java.version                  == 1.8.0_72 (Java 1)
java.vm.name                  == Java HotSpot(TM) Client VM
java.vm.vendor                == Oracle Corporation
java.vm.version               == 25.72-b15
java.vm.specification.name    == Java Virtual Machine Specification
java.vm.specification.vendor  == Oracle Corporation
java.vm.specification.version == 1.8
java.specification.name       == Java Platform API Specification
java.specification.vendor     == Oracle Corporation
java.specification.version    == 1.8
java.vendor                   == Oracle Corporation
java.vendor.url               == http://java.oracle.com/
java.class.version            == 52.0
java.library.path             == C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;.
java.compiler                 == null
java.home                     == C:\Users\<USER>\AppData\Local\Temp\********763\Windows\resource\jre
java.io.tmpdir                == C:\Users\<USER>\AppData\Local\Temp\
os.name                       == Windows 10
os.arch                       == x86
os.version                    == 10.0
path.separator                == ;
file.separator                == \
file.encoding                 == Cp1252
user.name                     == Admin
user.home                     == C:\Users\<USER>\Users\Admin\AppData\Local\Temp\********763\Windows
user.language                 == en
user.region                   == null
__________________________________________________________________________



Loading externalized properties

8. final log file name=C:\Program Files (x86)\fis2\UninstallerData\Logs\FIS_2_Install_07_31_2025_10_27_01.xml
#
# INSTALLING VM: C:\Program Files (x86)\fis2\java
#
System's temporary directory = C:\Users\<USER>\AppData\Local\Temp
key HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2 doesn't exist
XMLScriptWriter: No Installation Objects were skipped
File encoding detected: WINDOWS-1252
Retrying Installables deferred in pass 0
Deferral retries done because: 
There were no deferrals in the last pass.
SHUTDOWN REQUESTED

REGISTRY ALREADY STORED!

Exiting with exit code: 0
8. final log file name=C:\Program Files (x86)\fis2\UninstallerData\Logs\FIS_2_Install_07_31_2025_10_27_01.xml
]]>
	</STDERR>
	<STDOUT>
	<![CDATA[(X) commiting registry
(X) shutting down service manager
(X) cleaning up temporary directories
]]>
	</STDOUT>
</InstallationLog>
