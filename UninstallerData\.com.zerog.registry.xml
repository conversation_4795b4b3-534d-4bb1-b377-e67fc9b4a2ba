<?xml version="1.0" encoding="UTF-8"?>
<registry install_date="2025-07-31 10:27:01" version="1.1" last_modified="2025-07-31 10:27:14">
	<products>
		<product name="FIS 2" id="1e660ac6-1ec3-11b2-af7f-dae2252e4790" upgrade_id="1e660ac6-1ec3-11b2-af7f-dae2252e4790" version="1.0.1.5" copyright="Hapag-Lloyd AG 2018" info_url="http://fisprod.hlcl.com/fis2installer" support_url="http://fisprod.hlcl.com/fis2pages/main_prereqisites.html" location="C:\Program Files (x86)\fis2" last_modified="2025-07-31 10:27:14">
		<![CDATA[FIS 2 Application Launcher]]>
			<vendor name="Hapag-Lloyd AG" id="1e660ac7-1ec3-11b2-af7e-dae2252e4790" home_page="www.hapag-lloyd.com" email="<EMAIL>"/>
			<feature short_name="fisprod" name="fis2 Prod" last_modified="2025-07-31 10:27:14">
			<![CDATA[This installs the PRODUCTION environment application components.]]>
				<component ref_id="1e663ded-1ec3-11b2-afd1-dae2252e4790" version="1.0.0.9" location="C:\Program Files (x86)\fis2\UninstallerData\Uninstall fis2.exe"/>
				<component ref_id="014b6c64-1ed9-11b2-b906-edb9e7125146" version="1.0.0.0" location="C:\Program Files (x86)\fis2\java"/>
				<component ref_id="1e663ded-1ec3-11b2-afd0-dae2252e4790" version="1.0.0.9" location="C:\Program Files (x86)\fis2\fisprod.jar"/>
				<component ref_id="fe3396e7-1ed8-11b2-b7ae-edb9e7125146" version="1.0.0.0" location="C:\Program Files (x86)\fis2\hlcl.policy"/>
			</feature>
		</product>
	</products>
	<components>
		<component id="fe3396e7-1ed8-11b2-b7ae-edb9e7125146" version="1.0.0.0" name="AG- fisprod, fistest, fistraining" location="C:\Program Files (x86)\fis2\hlcl.policy">
			<resource keyfile="true" name="hlcl.policy" location="C:\Program Files (x86)\fis2\hlcl.policy" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="data" location="C:\Users\<USER>\fis2\data\data" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[false]]>
			</resource>
			<resource keyfile="false" name="Uninstall fis2.ico" location="C:\Program Files (x86)\fis2\UninstallerData\Uninstall fis2.ico" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="Uninstall fis2.lnk" location="C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\FIS 2\Uninstall fis2.lnk" type="shortcut" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[]]>
			</resource>
		</component>
		<component id="1e663ded-1ec3-11b2-afd0-dae2252e4790" version="1.0.0.9" name="C- Application" location="C:\Program Files (x86)\fis2\fisprod.jar" vendor="Hapag-Lloyd AG">
			<resource keyfile="false" name="prod" location="C:\Users\<USER>\fis2\cache\prod" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[true]]>
			</resource>
			<resource keyfile="true" name="fisprod.jar" location="C:\Program Files (x86)\fis2\fisprod.jar" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="fis2prod.bat" location="C:\Program Files (x86)\fis2\fis2prod.bat" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="fis2prod.ico" location="C:\Program Files (x86)\fis2\fis2prod.ico" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="FIS2.lnk" location="C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\FIS 2\FIS2.lnk" type="shortcut" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[]]>
			</resource>
		</component>
		<component id="1e663ded-1ec3-11b2-afd1-dae2252e4790" version="1.0.0.9" name="InstallAnywhere Uninstall Component" location="C:\Program Files (x86)\fis2\UninstallerData\Uninstall fis2.exe" vendor="Hapag-Lloyd AG">
			<resource keyfile="false" name="FIS_2_Install_07_31_2025_10_27_01.xml" location="C:\Program Files (x86)\fis2\UninstallerData\Logs\FIS_2_Install_07_31_2025_10_27_01.xml" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="fis2" location="C:\Program Files (x86)\fis2" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[false]]>
			</resource>
			<resource keyfile="false" name="java" location="C:\Program Files (x86)\fis2\java" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[false]]>
			</resource>
			<resource keyfile="false" name="fis2" location="C:\Users\<USER>\fis2" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[false]]>
			</resource>
			<resource keyfile="false" name="cache" location="C:\Users\<USER>\fis2\cache" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[false]]>
			</resource>
			<resource keyfile="false" name="cache" location="C:\Users\<USER>\fis2\cache" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[false]]>
			</resource>
			<resource keyfile="false" name="UninstallerData" location="C:\Program Files (x86)\fis2\UninstallerData" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[false]]>
			</resource>
			<resource keyfile="false" name="Logs" location="C:\Program Files (x86)\fis2\UninstallerData\Logs" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[true]]>
			</resource>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|983103||STRING|$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|983103|" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|DisplayName|STRING|FIS 2$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|DisplayName" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="uninstaller.jar" location="C:\Program Files (x86)\fis2\UninstallerData\uninstaller.jar" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="true" name="Uninstall fis2.exe" location="C:\Program Files (x86)\fis2\UninstallerData\Uninstall fis2.exe" type="executable" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="Uninstall fis2.lax" location="C:\Program Files (x86)\fis2\UninstallerData\Uninstall fis2.lax" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="resource" location="C:\Program Files (x86)\fis2\UninstallerData\resource" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[false]]>
			</resource>
			<resource keyfile="false" name="iawin32.dll" location="C:\Program Files (x86)\fis2\UninstallerData\resource\iawin32.dll" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win64_32_x64.exe" location="C:\Program Files (x86)\fis2\UninstallerData\resource\win64_32_x64.exe" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="remove.exe" location="C:\Program Files (x86)\fis2\UninstallerData\resource\remove.exe" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="invoker.exe" location="C:\Program Files (x86)\fis2\UninstallerData\resource\invoker.exe" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|DisplayName|STRING|FIS 2$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|DisplayName" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|UninstallString|STRING|&quot;C:\Program Files (x86)\fis2\UninstallerData\Uninstall fis2.exe&quot;$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|UninstallString" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|Publisher|STRING|Hapag-Lloyd AG$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|Publisher" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|URLInfoAbout|STRING|www.hapag-lloyd.com$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|URLInfoAbout" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|URLUpdateInfo|STRING|http://fisprod.hlcl.com/fis2installer$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|URLUpdateInfo" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|Contact|STRING|<EMAIL>$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|Contact" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|ProductID|STRING|1e660ac6-1ec3-11b2-af7f-dae2252e4790$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|ProductID" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|InstallLocation|STRING|C:\Program Files (x86)\fis2$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|InstallLocation" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|InstallDate|STRING|Thu Jul 31 10:27:12 EAT 2025$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|InstallDate" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|HelpLink|STRING|http://fisprod.hlcl.com/fis2pages/main_prereqisites.html$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|HelpLink" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|DisplayVersion|STRING|1.0.1.5$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|DisplayVersion" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="win_reg_entry" location="$INSTALLED$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|Comments|STRING|FIS 2 Application Launcher$DELETE$|HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FIS 2|131103|Comments" type="win_reg_entry" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="data" location="C:\Users\<USER>\fis2\data" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[false]]>
			</resource>
			<resource keyfile="false" name="data" location="C:\Users\<USER>\fis2\data" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[false]]>
			</resource>
			<resource keyfile="false" name="FIS 2" location="C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\FIS 2" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[false]]>
			</resource>
			<resource keyfile="false" name="FIS 2" location="C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\FIS 2" type="directory" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[false]]>
			</resource>
			<resource keyfile="false" name="regid.2013-09.com.hlag_a45c90b6-1f0f-11b2-84f7-827dbb813ea4.swidtag" location="C:\Program Files (x86)\fis2\regid.2013-09.com.hlag_a45c90b6-1f0f-11b2-84f7-827dbb813ea4.swidtag" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="regid.2013-09.com.hlag_a45c90b6-1f0f-11b2-84f7-827dbb813ea4.swidtag" location="C:\ProgramData\regid.2013-09.com.hlag_a45c90b6-1f0f-11b2-84f7-827dbb813ea4.swidtag" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name="ZGWin32LaunchHelper.exe" location="C:\Program Files (x86)\fis2\UninstallerData\resource\ZGWin32LaunchHelper.exe" type="file" uninstall="true" rollbackEnabledCancel="true"/>
			<resource keyfile="false" name=".com.zerog.registry.xml" location="C:\Program Files (x86)\fis2\UninstallerData\.com.zerog.registry.xml" type="file" uninstall="true" rollbackEnabledCancel="true"/>
		</component>
		<component id="014b6c64-1ed9-11b2-b906-edb9e7125146" version="1.0.0.0" name="InstallAnywhere VM Component" location="C:\Program Files (x86)\fis2\java" vendor="Hapag-Lloyd AG">
			<resource keyfile="true" name="java" location="C:\Program Files (x86)\fis2\java" type="post_processed_dir" uninstall="true" rollbackEnabledCancel="true">
			<![CDATA[true]]>
			</resource>
		</component>
	</components>
</registry>
