@echo off
rem /****************************************************************************/
rem /*
rem /* 1. Program Name       :  fis2prod.bat
rem /*
rem /* 2. Language           :  NT Batch
rem /*
rem /* 3. Author             :  <PERSON><PERSON>, Hapag-Lloyd AG  16.09.2001
rem /*
rem /* 4. Description        :  This is the script to start FIS2 on Windows Plattforms
rem /*
rem /* 5. Operating          :  a shortcut is created during installation on the desktop
rem /*
rem /* 6. Environment        :  JRE >= 1.6.32 is needed
rem /*
rem /* 7. Subroutines        :  --
rem /*
rem /* 8. Call               :  fis2prod.bat [ecf]
rem /*                          ecf : Expert Command Line
rem /*
rem /* 9. Return Codes       :  0 :  ok
rem /*                         -1 :  error
rem /*                          1 :  error
rem /*
rem /****************************************************************************/
rem /* 10. Changes
rem /*
rem /* DD/MM/YYYY  WHO   WHAT
rem /* 10/05/2002  HR    ver=02 fis2hlcmserver is optional  
rem /* 22/10/2002  HR    ver=03 command line argument wird übergeben
rem /* 29/10/2002  HR    ver=04 ecf port wird übergeben
rem /* 11/10/2005  HR    ver=07 support for Java 1.5.0_03 (external)
rem /* 11/10/2005  HR    ver=09 do not modify path variable (problems with browser)
rem /* 18/04/2013  JR    ver=11 change jvm memory parameters
rem /****************************************************************************/

setlocal
set classpath=C:\Program Files (x86)\fis2\fisprod.jar;%classpath%

set fis2cache=C:\Users\<USER>\fis2\cache\prod
set fis2xchange=C:\Users\<USER>\fis2\data
set fis2baseserver=fisprod.hlcl.com
set fis2baseport=80
set fis2browser=C:\Program Files\Internet Explorer\iexplore.exe
set fis2logserver=fisprod.hlcl.com
set fis2logport=80
set httpproxyHost=
set httpproxyPort=
set httpproxyID=
set javasecuritypolicy=C:\Program Files (x86)\fis2\hlcl.policy

start /D"C:\Program Files (x86)\fis2\java\bin" .\javaw.exe -Xms256M -Xmx768M -XX:MaxPermSize=256m -Dsun.net.client.defaultConnectTimeout=30000 -Dsun.net.client.defaultReadTimeout=900000 -Dfis2.script.ver=11 -Dhttp.proxyHost=%httpproxyHost% -Dhttp.proxyPort=%httpproxyPort% -Dhttp.proxyID="%httpproxyID%" -Dfis2.log.server=%fis2logserver% -Dfis2.log.port=%fis2logport% -Dfis2.browser="%fis2browser%" -Dfis2.cache="%fis2cache%" -Dfis2.xchange="%fis2xchange%" -Dfis2.base.server="%fis2baseserver%" -Dfis2.base.port=%fis2baseport% -Dfis2.ecf.port=7654 -Djava.security.manager -Djava.security.policy="%javasecuritypolicy%"  com.hlcl.deploy.AppLauncher %*
endlocal
