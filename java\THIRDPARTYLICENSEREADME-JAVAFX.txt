﻿DO NOT TRANSLATE OR LOCALIZE

***************************************************************************

%%The following software may be included in this product:
Microsoft DirectShow - Base Classes

Use of any of this software is governed by the terms of the license below:

MSDN - Information on Terms of Use

Updated: February 13, 2008

ON THIS PAGE

    * ACCEPTANCE OF TERMS
    * PRIVACY AND PROTECTION OF PERSONAL INFORMATION
    * NOTICE SPECIFIC TO APIs AVAILABLE ON THIS WEB SITE
    * NOTICE SPECIFIC TO SOFTWARE AVAILABLE ON THIS WEB SITE
    * NOTICE SPECIFIC TO DOCUMENTATION AVAILABLE ON THIS WEB SITE
    * NOTICES REGARDING SOFTWARE, DOCUMENTATION, APIS AND SERVICES AVAILABLE ON
THIS WEB SITE
    * RESERVATION OF RIGHTS
    * MEMBER ACCOUNT, PASSWORD, AND SECURITY
    * NO UNLAWFUL OR PROHIBITED USE
    * USE OF SERVICES
    * MATERIALS PROVIDED TO MICROSOFT OR POSTED AT ANY MICROSOFT WEB SITE
    * NOTICES AND PROCEDURE FOR MAKING CLAIMS OF COPYRIGHT INFRINGEMENT
    * LINKS TO THIRD PARTY SITES
    * UNSOLICITED IDEA SUBMISSION POLICY
    * COPYRIGHT NOTICE & FAQ
    * TRADEMARKS

ACCEPTANCE OF TERMS.

Microsoft provides you with access to a variety of resources on this website
(“Web Site”), including documentation and other product information
(collectively the “Documentation”), download areas, communication forums, and
other services (collectively "Services"), software, including developer tools
and sample code (collectively “Software”), and Application Program Interface
information (“APIs”).  The Documentation, Services, Software, and APIs
(including any updates, enhancements, new features, and/or the addition of any
new Web properties to the Web Site), are subject to the following Terms of Use
("TOU"), unless we have provided those items to you under more specific terms,
in which case, those more specific terms will apply to the relevant item.
Microsoft reserves the right to update the TOU at any time without notice to
you. The most current version of the TOU can be reviewed by clicking on the
"Terms of Use" hypertext link located at the bottom of our Web pages.

Top of page
PRIVACY AND PROTECTION OF PERSONAL INFORMATION.

See the Privacy Statement disclosures relating to the collection and use of your
information.

Top of page
NOTICE SPECIFIC TO APIS AVAILABLE ON THIS WEB SITE.

Microsoft publishes information on a number of APIs on this Web Site.  Microsoft
will not assert any of its patent rights on account of your products calling
these APIs in order to receive services from the Microsoft product that exposes
the APIs. 

Top of page
NOTICE SPECIFIC TO SOFTWARE AVAILABLE ON THIS WEB SITE.

All Software is the copyrighted work of Microsoft and/or its suppliers. Use of
the Software is governed by the terms of the end user license agreement, if any,
which accompanies or is included with the Software ("License Agreement").

If Microsoft makes Software available on this Web Site without a License
Agreement, you may use such Software to design, develop and test your programs
to run on Microsoft products and services.

If Microsoft makes any code marked as “sample” available on this Web Site
without a License Agreement, then that code is licensed to you under the terms
of the Microsoft Limited Public License.

The Software is made available for download solely for use by end users
according to the License Agreement or these TOU. Any reproduction or
redistribution of the Software not in accordance with the License Agreement or
these TOU is expressly prohibited.

WITHOUT LIMITING THE FOREGOING, COPYING OR REPRODUCTION OF THE SOFTWARE TO ANY
OTHER SERVER OR LOCATION FOR FURTHER REPRODUCTION OR REDISTRIBUTION IS EXPRESSLY
PROHIBITED, UNLESS SUCH REPRODUCTION OR REDISTRIBUTION IS EXPRESSLY PERMITTED BY
THE LICENSE AGREEMENT ACCOMPANYING SUCH SOFTWARE.

FOR YOUR CONVENIENCE, MICROSOFT MAY MAKE AVAILABLE ON THIS WEB SITE, TOOLS AND
UTILITIES FOR USE AND/OR DOWNLOAD. MICROSOFT DOES NOT MAKE ANY ASSURANCES WITH
REGARD TO THE ACCURACY OF THE RESULTS OR OUTPUT THAT DERIVES FROM SUCH USE OF
ANY SUCH TOOLS AND UTILITIES. PLEASE RESPECT THE INTELLECTUAL PROPERTY RIGHTS OF
OTHERS WHEN USING THE TOOLS AND UTILITIES MADE AVAILABLE ON THIS WEB SITE.

RESTRICTED RIGHTS LEGEND. Any Software which is downloaded from the Web Site for
or on behalf of the United States of America, its agencies and/or
instrumentalities ("U.S. Government"), is provided with Restricted Rights... Use,
duplication, or disclosure by the U.S. Government is subject to restrictions as
set forth in subparagraph (c)(1)(ii) of the Rights in Technical Data and
Computer Software clause at DFARS ************ or subparagraphs (c)(1) and (2)
of the Commercial Computer Software - Restricted Rights at 48 CFR 52.227-19, as
applicable. Manufacturer is Microsoft Corporation, One Microsoft Way, Redmond,
WA 98052-6399.

Top of page
NOTICE SPECIFIC TO DOCUMENTATION AVAILABLE ON THIS WEB SITE.

All Documentation is the copyrighted work of Microsoft and/or its suppliers. Use
of the Documentation is governed by the terms of the license agreement, if any,
which accompanies or is included with the Documentation ("Documentation License
Agreement").

If Documentation is made available to you on this Web Site without a
Documentation License Agreement, then You may annotate, translate, and make a
reasonable number of copies of the Documentation for your internal use in
designing, developing, and testing your software, products and services, and you
may distribute a reasonable amount of portions of the Documentation as necessary
to document your software, products, and services.  You may not publish any such
annotations or translations.  You must preserve the below copyright notice in
all copies of the Documentation and ensure that both the copyright notice and
this permission notice appear in those copies.  Accredited educational
institutions, such as K-12, universities, private/public colleges, and state
community colleges, may download and reproduce the Documentation for
distribution in the classroom. Distribution outside the classroom requires
express written permission. Use for any other purpose is expressly prohibited

The Documentation does not include the design or layout of the Microsoft.com Web
site or any other Microsoft owned, operated, licensed or controlled site.
Elements of Microsoft Web sites are protected by trade dress, trademark, unfair
competition, and other laws and may not be copied or imitated in whole or in
part. No logo, graphic, sound or image from any Microsoft Web site may be copied
or retransmitted unless expressly permitted by Microsoft.

THE DOCUMENTATION AND RELATED GRAPHICS PUBLISHED ON THE WEB SITE COULD INCLUDE
TECHNICAL INACCURACIES OR TYPOGRAPHICAL ERRORS. CHANGES ARE PERIODICALLY ADDED
TO THE INFORMATION IN THIS WEB SITE. MICROSOFT AND/OR ITS RESPECTIVE SUPPLIERS
MAY MAKE IMPROVEMENTS AND/OR CHANGES IN THE PRODUCT(S) AND/OR THE PROGRAM(S)
DESCRIBED IN THIS WEB SITE AT ANY TIME.

Top of page
NOTICES REGARDING SOFTWARE, DOCUMENTATION, APIS AND SERVICES AVAILABLE ON THIS
WEB SITE.

THE SOFTWARE, DOCUMENTATION, APIS, AND SERVICES ARE WARRANTED, IF AT ALL, ONLY
ACCORDING TO THE TERMS OF ASEPARATE AGREEMENT THAT COVERS THE APPLICABLE
SOFTWARE, DOCUMENTATION, APIS, OR SERVICES. EXCEPT AS WARRANTED IN THAT SEPARATE
AGREEMENT (IF ANY), MICROSOFT CORPORATION HEREBY DISCLAIMS ALL WARRANTIES AND
CONDITIONS WITH REGARD TO THE SOFTWARE, DOCUMENTATION, APIS, AND SERVICES,
INCLUDING ALL WARRANTIES AND CONDITIONS OF MERCHANTABILITY, WHETHER EXPRESS,
IMPLIED OR STATUTORY, FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT.

IN NO EVENT SHALL MICROSOFT AND/OR ITS RESPECTIVE SUPPLIERS BE LIABLE FOR ANY
SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING
FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE
OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF SOFTWARE, DOCUMENTATION, APIS, PROVISION OF OR FAILURE TO PROVIDE
SERVICES, OR INFORMATION AVAILABLE FROM ANY OF THE FOREGOING SOFTWARE,
DOCUMENTATION, APIS OR SERVICES.

Top of page
RESERVATION OF RIGHTS.

Microsoft reserves all rights not expressly granted under these TOU, and no
other rights are granted under these TOU by implication or estoppel or otherwise. 

Top of page
MEMBER ACCOUNT, PASSWORD, AND SECURITY.

If any of the Services requires you to open an account, you must complete the
registration process by providing us with current, complete and accurate
information as prompted by the applicable registration form. You also will
choose a password and a user name. You are entirely responsible for maintaining
the confidentiality of your password and account. Furthermore, you are entirely
responsible for any and all activities that occur under your account. You agree
to notify Microsoft immediately of any unauthorized use of your account or any
other breach of security. Microsoft will not be liable for any loss that you may
incur as a result of someone else using your password or account, either with or
without your knowledge. However, you could be held liable for losses incurred by
Microsoft or another party due to someone else using your account or password.
You may not use anyone else's account at any time, without the permission of the
account holder.

Top of page
NO UNLAWFUL OR PROHIBITED USE.

As a condition of your use of the Services, you will not use the Services for
any purpose that is unlawful or prohibited by these terms, conditions, and
notices. You may not use the Services in any manner that could damage, disable,
overburden, or impair any Microsoft server, or the network(s) connected to any
Microsoft server, or interfere with any other party's use and enjoyment of any
Services. You may not attempt to gain unauthorized access to any Services, other
accounts, computer systems or networks connected to any Microsoft server or to
any of the Services, through hacking, password mining or any other means. You
may not obtain or attempt to obtain any materials or information through any
means not intentionally made available through the Services.

Top of page
USE OF SERVICES.

The Services may contain e-mail services, bulletin board services, chat areas,
news groups, forums, communities, personal web pages, calendars, photo albums,
file cabinets and/or other message or communication facilities designed to
enable you to communicate with others (each a "Communication Service" and
collectively "Communication Services"). You agree to use the Communication
Services only to post, send and receive messages and material that are proper
and, when applicable, related to the particular Communication Service. By way of
example, and not as a limitation, you agree that when using the Communication
Services, you will not:

    * Use the Communication Services in connection with surveys, contests,
pyramid schemes, chain letters, junk email, spamming or any duplicative or
unsolicited messages (commercial or otherwise).
    * Defame, abuse, harass, stalk, threaten or otherwise violate the legal
rights (such as rights of privacy and publicity) of others.
    * Publish, post, upload, distribute or disseminate any inappropriate,
profane, defamatory, obscene, indecent or unlawful topic, name, material or
information.
    * Upload, or otherwise make available, files that contain images,
photographs, software or other material protected by intellectual property laws,
including, by way of example, and not as limitation, copyright or trademark laws
(or by rights of privacy or publicity) unless you own or control the rights
thereto or have received all necessary consent to do the same.
    * Use any material or information, including images or photographs, which
are made available through the Services in any manner that infringes any
copyright, trademark, patent, trade secret, or other proprietary right of any party.
    * Upload files that contain viruses, Trojan horses, worms, time bombs,
cancelbots, corrupted files, or any other similar software or programs that may
damage the operation of another's computer or property of another.
    * Advertise or offer to sell or buy any goods or services for any business
purpose, unless such Communication Services specifically allows such messages.
    * Download any file posted by another user of a Communication Service that
you know, or reasonably should know, cannot be legally reproduced, displayed,
performed, and/or distributed in such manner.
    * Falsify or delete any copyright management information, such as author
attributions, legal or other proper notices or proprietary designations or
labels of the origin or source of software or other material contained in a file
that is uploaded.
    * Restrict or inhibit any other user from using and enjoying the
Communication Services.
    * Violate any code of conduct or other guidelines which may be applicable
for any particular Communication Service.
    * Harvest or otherwise collect information about others, including e-mail
addresses.
    * Violate any applicable laws or regulations.
    * Create a false identity for the purpose of misleading others.
    * Use, download or otherwise copy, or provide (whether or not for a fee) to
a person or entity any directory of users of the Services or other user or usage
information or any portion thereof.

Microsoft has no obligation to monitor the Communication Services. However,
Microsoft reserves the right to review materials posted to the Communication
Services and to remove any materials in its sole discretion. Microsoft reserves
the right to terminate your access to any or all of the Communication Services
at any time, without notice, for any reason whatsoever.

Microsoft reserves the right at all times to disclose any information as
Microsoft deems necessary to satisfy any applicable law, regulation, legal
process or governmental request, or to edit, refuse to post or to remove any
information or materials, in whole or in part, in Microsoft's sole discretion.

Always use caution when giving out any personally identifiable information about
yourself or your children in any Communication Services. Microsoft does not
control or endorse the content, messages or information found in any
Communication Services and, therefore, Microsoft specifically disclaims any
liability with regard to the Communication Services and any actions resulting
from your participation in any Communication Services. Managers and hosts are
not authorized Microsoft spokespersons, and their views do not necessarily
reflect those of Microsoft.

Materials uploaded to the Communication Services may be subject to posted
limitations on usage, reproduction and/or dissemination; you are responsible for
adhering to such limitations if you download the materials.

Top of page
MATERIALS PROVIDED TO MICROSOFT OR POSTED AT ANY MICROSOFT WEB SITE.

Microsoft does not claim ownership of the materials you provide to Microsoft
(including feedback and suggestions) or post, upload, input or submit to any
Services or its associated services for review by the general public, or by the
members of any public or private community, (each a "Submission" and
collectively "Submissions"). However, by posting, uploading, inputting,
providing or submitting ("Posting") your Submission you are granting Microsoft,
its affiliated companies, necessary sublicensees (including third parties whose
products , technologies and services use or interface with any specific parts of
a Microsoft software or service that includes the Submission) , without charge,
the right  to use, share and commercialize your Submission in any way and for
any purpose. You will not give any Submission that is subject to a license that
requires Microsoft to license its software or documentation to third parties
because we include your Submission in them.

Microsoft is under no obligation to post or use any Submission you may provide,
and Microsoft may remove any Submission at any time in its sole discretion.

By Posting a Submission you warrant and represent that you own or otherwise
control all of the rights to your Submission as described in these TOU
including, without limitation, all the rights necessary for you to Post the
Submissions.

In addition to the warranty and representation set forth above, by Posting a
Submission that contains images, photographs, pictures or that are otherwise
graphical in whole or in part ("Images"), you warrant and represent that (a) you
are the copyright owner of such Images, or that the copyright owner of such
Images has granted you permission to use such Images or any content and/or
images contained in such Images consistent with the manner and purpose of your
use and as otherwise permitted by these TOU, (b) you have the rights necessary
to grant the licenses and sublicenses described in these TOU, and (c) that each
person depicted in such Images, if any, has provided consent to the use of the
Images as set forth in these TOU, including, by way of example, and not as a
limitation, the distribution, public display and reproduction of such Images. By
Posting Images, you are granting (a) to all members of your private community
(for each such Images available to members of such private community), and/or
(b) to the general public (for each such Images available anywhere on the
Services or Web Site, other than a private community), permission to use your
Images in connection with the use, as permitted by these TOU, of any of the
Services or Web Site, (including, by way of example, and not as a limitation,
making prints and gift items which include such Images), and including, without
limitation, a non-exclusive, world-wide, royalty-free license to: copy,
distribute, transmit, publicly display, publicly perform, reproduce, edit,
translate and reformat your Images without having your name attached to such
Images, and the right to sublicense such rights to any supplier of the Services.
The licenses granted in the preceding sentences for a Images will terminate at
the time you completely remove such Images from the Services or Web Site,
provided that such termination shall not affect any licenses granted in
connection with such Images prior to the time you completely remove such Images.
No compensation will be paid with respect to the use of your Images.

Top of page
NOTICES AND PROCEDURE FOR MAKING CLAIMS OF COPYRIGHT INFRINGEMENT.

Pursuant to Title 17, United States Code, Section 512(c)(2), notifications of
claimed copyright infringement should be sent to Service Provider's Designated
Agent. ALL INQUIRIES NOT RELEVANT TO THE FOLLOWING PROCEDURE WILL NOT RECEIVE A
RESPONSE.

See Notice and Procedure for Making Claims of Copyright Infringement.

Top of page
LINKS TO THIRD PARTY SITES.

THE LINKS IN THIS AREA WILL LET YOU LEAVE MICROSOFT'S SITE. THE LINKED SITES ARE
NOT UNDER THE CONTROL OF MICROSOFT AND MICROSOFT IS NOT RESPONSIBLE FOR THE
CONTENTS OF ANY LINKED SITE OR ANY LINK CONTAINED IN A LINKED SITE, OR ANY
CHANGES OR UPDATES TO SUCH SITES. MICROSOFT IS NOT RESPONSIBLE FOR WEBCASTING OR
ANY OTHER FORM OF TRANSMISSION RECEIVED FROM ANY LINKED SITE. MICROSOFT IS
PROVIDING THESE LINKS TO YOU ONLY AS A CONVENIENCE, AND THE INCLUSION OF ANY
LINK DOES NOT IMPLY ENDORSEMENT BY MICROSOFT OF THE SITE.

Top of page
UNSOLICITED IDEA SUBMISSION POLICY.

MICROSOFT OR ANY OF ITS EMPLOYEES DO NOT ACCEPT OR CONSIDER UNSOLICITED IDEAS,
INCLUDING IDEAS FOR NEW ADVERTISING CAMPAIGNS, NEW PROMOTIONS, NEW PRODUCTS OR
TECHNOLOGIES, PROCESSES, MATERIALS, MARKETING PLANS OR NEW PRODUCT NAMES. PLEASE
DO NOT SEND ANY ORIGINAL CREATIVE ARTWORK, SAMPLES, DEMOS, OR OTHER WORKS... THE
SOLE PURPOSE OF THIS POLICY IS TO AVOID POTENTIAL MISUNDERSTANDINGS OR DISPUTES
WHEN MICROSOFT'S PRODUCTS OR MARKETING STRATEGIES MIGHT SEEM SIMILAR TO IDEAS
SUBMITTED TO MICROSOFT. SO, PLEASE DO NOT SEND YOUR UNSOLICITED IDEAS TO
MICROSOFT OR ANYONE AT MICROSOFT. IF, DESPITE OUR REQUEST THAT YOU NOT SEND US
YOUR IDEAS AND MATERIALS, YOU STILL SEND THEM, PLEASE UNDERSTAND THAT MICROSOFT
MAKES NO ASSURANCES THAT YOUR IDEAS AND MATERIALS WILL BE TREATED AS
CONFIDENTIAL OR PROPRIETARY.

Top of page
COPYRIGHT NOTICE & FAQ.

© 2008 Microsoft Corporation. All rights reserved.

The following is provided for informational purposes only and should not be
construed as legal advice. If you need legal advice, contact a lawyer.

What is copyright?

Copyright law protects original works, such as websites, books, music,
paintings, photos and video. A work is “original” if it contains some elements
you created and did not borrow from others. Typically, when you create an
original work, you own the copyright. As the copyright owner, you can control
how others use your work. For example, if you write a movie script, you have the
right to, and can prevent others from, copying your script, sharing it with
others (“distributing it”), making a movie or book from your script (a
“derivative work”), or publicly performing your script as a play or movie. You
also have the ability to sell or give away these rights. In other words, you
could sell the right to make a movie based on your script to a movie studio.

If you use someone else’s copyrighted materials without permission, that use
generally violates the copyright owner's exclusive rights, and is copyright
infringement. So if you create a new work and include parts of other people’s
works in it (such as an existing photo, lengthy quotes from a book or a loop
from a song), you must own or have permission to use the elements you borrow.
For example, if your script is based on an existing popular series, you should
obtain permission to use the elements you borrow from the series.

Copyright law is different from the law of personal property. If you buy a
physical object, such as a movie on DVD, you own the physical object. You do
not, however, obtain ownership of the “copyrights” (the rights to make copies,
distribute, make derivatives and publicly perform or display) in the content of
the movie. The fact that you have obtained physical possession of a DVD does not
automatically grant you the right to copy or share it.

If you make your own movie, it may include many copyrighted works in it. So, if
you decide to make a movie based on your script, you must either create all
elements of it on your own, or have permission to use the elements you borrow.
Especially keep in mind that photos or artwork hanging on the walls of your sets
and music on the soundtrack (even if you own the CD or MP3) may be copyrighted.
You should not include copyrighted works such as these in your movie without
authorization.

A few other things to keep in mind are:

   1. Just because a work does not include a copyright notice (e.g., © 2006
Microsoft Corporation) does not mean the work is in the public domain. Copyright
notices are generally not required for works to be protected by copyright.
   2. Just because a work is easily available on the internet or elsewhere does
not mean you may use the work freely. Look for terms of use, such as Creative
Commons, that explain how works you find on the Internet may be used.

Isn't it in the public domain?

Just because a work is freely available, does not mean it is in the “public
domain.” Copyright is for a limited term; it does not last forever. In the
copyright context, “public domain” means the copyright term has expired. Once a
work is in the public domain, it may be freely used without permission from the
copyright owner.

Determining the term of copyright can be complex, particularly because copyright
laws vary from country to country. Also, even if the copyright on a work has
expired, you should be careful about how you use a public domain work. For
example, a book may be in the public domain, but it might not be ok to scan the
book cover to cover and post it on the internet. This is because the particular
version of the book may contain new copyrightable material that is not in the
public domain, such as cover art or footnotes.

What about fair use?

In limited situations, you can use copyrighted works without permission from the
copyright holder. It can be difficult to figure out whether use of copyrighted
works without permission is legal, though, because the laws in this area are
often vague and vary from country to country.

The copyright law in the United States has a doctrine called “fair use”. Fair
use provides a defense to copyright infringement in some circumstances. For
example, fair use allows documentary filmmakers to use very short clips of
copyrighted movies, music and news footage without permission from the copyright
owner. Fair use is a difficult concept because determining whether something is
a fair use involves weighing four factors. Unfortunately, weighing the fair use
factors rarely results in a clear-cut answer.

Rather than applying a fair use test, many other countries have specific
exceptions to copyright infringement. The number and type of exceptions vary by
country, but they frequently allow copyrighted materials to be used without
permission from the copyright holder for activities such as nonprofit research,
teaching, news reporting, or private study.

If you incorrectly decide that something is a fair use or falls into an
exception to copyright infringement, you could be held criminally and civilly
liable and have to pay damages. We suggest you talk to a lawyer if you have
questions regarding fair uses of copyrighted works.

What happens if you upload copyrighted materials to one of our websites without
permission?

By law, we are required to take down videos, music, photographs or other content
you upload onto a website hosted by Microsoft if we learn that it infringes
someone else’s copyright. If you believe that we have mistakenly taken down
content you uploaded that you own or have permission to upload, you can also let
us know that. Finally, if you upload infringing content repeatedly, we will
terminate your account and you could face criminal and civil penalties. So
please, respect other people’s copyrights.

What if my stuff is on a Microsoft website without my permission?

If you believe that anything on a website hosted by Microsoft infringes your
copyright, let us know. Just provide us with the information requested here and
we will see that your copyrighted works are taken down.

What if I don't want my website crawled?

Microsoft search services (MSN Search and Windows Live Search) follow the Robots
Exclusion Standards. This means that you can control which pages Microsoft
search engines index and how often Microsoft bots access your website. To learn
how to do so, or for more information regarding Microsoft’s webcrawling and site
indexing practices, please visit http://search.msn.com/docs/siteowner.aspx.

Top of page
TRADEMARKS.

Trademark information is available at
http://www.microsoft.com/library/toolbar/3.0/trademarks/en-us.mspx.

Any rights not expressly granted herein are reserved.

Send your questions to the appropriate contact as listed below:

    * Microsoft Web properties, contact <EMAIL>.
    * MSN Web properties, contact <EMAIL>.
    * Hotmail, contact <EMAIL>; for spam/privacy issues, contact
abuse@hotmail.<NAME_EMAIL>.
    * Piracy questions can be <NAME_EMAIL> or by calling
1-800-R-U-LEGIT.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Microsoft Public License (Ms-PL)
Mon, 2007-10-15 19:23 — nelson

This license governs use of the accompanying software. If you use the software, you
accept this license. If you do not accept the license, do not use the software.

1. Definitions
The terms "reproduce," "reproduction," "derivative works," and "distribution"
have the
same meaning here as under U.S. copyright law.
A "contribution" is the original software, or any additions or changes to the
software.
A "contributor" is any person that distributes its contribution under this license.
"Licensed patents" are a contributor's patent claims that read directly on its
contribution.

2. Grant of Rights
(A) Copyright Grant- Subject to the terms of this license, including the license
conditions and limitations in section 3, each contributor grants you a
non-exclusive, worldwide, royalty-free copyright license to reproduce its
contribution, prepare derivative works of its contribution, and distribute its
contribution or any derivative works that you create.
(B) Patent Grant- Subject to the terms of this license, including the license
conditions and limitations in section 3, each contributor grants you a
non-exclusive, worldwide, royalty-free license under its licensed patents to
make, have made, use, sell, offer for sale, import, and/or otherwise dispose of
its contribution in the software or derivative works of the contribution in the
software.

3. Conditions and Limitations
(A) No Trademark License- This license does not grant you rights to use any
contributors' name, logo, or trademarks.
(B) If you bring a patent claim against any contributor over patents that you
claim are infringed by the software, your patent license from such contributor
to the software ends automatically.
(C) If you distribute any portion of the software, you must retain all
copyright, patent, trademark, and attribution notices that are present in the
software.
(D) If you distribute any portion of the software in source code form, you may
do so only under this license by including a complete copy of this license with
your distribution. If you distribute any portion of the software in compiled or
object code form, you may only do so under a license that complies with this
license.
(E) The software is licensed "as-is." You bear the risk of using it. The
contributors give no express warranties, guarantees or conditions. You may have
additional consumer rights under your local laws which this license cannot
change. To the extent permitted under your local laws, the contributors exclude
the implied warranties of merchantability, fitness for a particular purpose and
non-infringement.

***************************************************************************

%%The following software may be included in this product:
Apache Batik

Use of any of this software is governed by the terms of the license below:

                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution..."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

***************************************************************************

%%The following software may be included in this product:
ASM

Use of any of this software is governed by the terms of the license below:

Copyright (c) 2000-2005 INRIA, France Telecom
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

3. Neither the name of the copyright holders nor the names of its
   contributors may be used to endorse or promote products derived from
   this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
THE POSSIBILITY OF SUCH DAMAGE.

***************************************************************************

%%The following software may be included in this product:
JPEG

Use of any of this software is governed by the terms of the license below:

Taken from code......

LEGAL ISSUES
============

In plain English:

1. We don't promise that this software works.  (But if you find any bugs,
   please let us know!)
2. You can use this software for whatever you want.  You don't have to pay us.
3. You may not pretend that you wrote this software.  If you use it in a
   program, you must acknowledge somewhere in your documentation that
   you've used the IJG code.

In legalese:

The authors make NO WARRANTY or representation, either express or implied,
with respect to this software, its quality, accuracy, merchantability, or
fitness for a particular purpose.  This software is provided "AS IS", and you,
its user, assume the entire risk as to its quality and accuracy.

This software is copyright (C) 1991-1998, Thomas G. Lane.
All Rights Reserved except as specified below.

Permission is hereby granted to use, copy, modify, and distribute this
software (or portions thereof) for any purpose, without fee, subject to these
conditions:
(1) If any part of the source code for this software is distributed, then this
README file must be included, with this copyright and no-warranty notice
unaltered; and any additions, deletions, or changes to the original files
must be clearly indicated in accompanying documentation.
(2) If only executable code is distributed, then the accompanying
documentation must state that "this software is based in part on the work of
the Independent JPEG Group".
(3) Permission for use of this software is granted only if the user accepts
full responsibility for any undesirable consequences; the authors accept
NO LIABILITY for damages of any kind.

These conditions apply to any software derived from or based on the IJG code,
not just to the unmodified library.  If you use our work, you ought to
acknowledge us.

Permission is NOT granted for the use of any IJG author's name or company name
in advertising or publicity relating to this software or products derived from
it.  This software may be referred to only as "the Independent JPEG Group's
software".

We specifically permit and encourage the use of this software as the basis of
commercial products, provided that all warranty or liability claims are
assumed by the product vendor.


ansi2knr.c is included in this distribution by permission of L. Peter Deutsch,
sole proprietor of its copyright holder, Aladdin Enterprises of Menlo Park, CA.
ansi2knr.c is NOT covered by the above copyright and conditions, but instead
by the usual distribution terms of the Free Software Foundation; principally,
that you must include source code if you redistribute it.  (See the file
ansi2knr.c for full details.)  However, since ansi2knr.c is not needed as part
of any program generated from the IJG code, this does not limit you more than
the foregoing paragraphs do.

The Unix configuration script "configure" was produced with GNU Autoconf.
It is copyright by the Free Software Foundation but is freely distributable.
The same holds for its supporting scripts (config.guess, config.sub,
ltconfig, ltmain.sh).  Another support script, install-sh, is copyright
by M.I.T. but is also freely distributable.

It appears that the arithmetic coding option of the JPEG spec is covered by
patents owned by IBM, AT&T, and Mitsubishi.  Hence arithmetic coding cannot
legally be used without obtaining one or more licenses.  For this reason,
support for arithmetic coding has been removed from the free JPEG software.
(Since arithmetic coding provides only a marginal gain over the unpatented
Huffman mode, it is unlikely that very many implementations will support it.)
So far as we are aware, there are no patent restrictions on the remaining
code.

The IJG distribution formerly included code to read and write GIF files.
To avoid entanglement with the Unisys LZW patent, GIF reading support has
been removed altogether, and the GIF writer has been simplified to produce
"uncompressed GIFs".  This technique does not use the LZW algorithm; the
resulting GIF files are larger than usual, but are readable by all standard
GIF decoders.

We are required to state that
    "The Graphics Interchange Format(c) is the Copyright property of
    CompuServe Incorporated.  GIF(sm) is a Service Mark property of
    CompuServe Incorporated."

Additional License(s)

"copyright"

***************************************************************************

MD5 License 
"THE BEER-WARE LICENSE" (Revision 42):
 wrote this file.  As long as you retain this
notice you can do whatever you want with this stuff. If we meet some
day, and you think this stuff is worth it, you can buy me a beer in
return.   Poul-Henning Kamp





***************************************************************************


%%The following software may be included in this product:
ANTLR Java runtime binary only jar

Use of any of this software is governed by the terms of the license below:

ANTLR 3 License

[The BSD License]
Copyright (c) 2003-2007, Terence Parr
All rights reserved.

Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

    * Redistributions of source code must retain the above copyright notice,
this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright notice,
this list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.
    * Neither the name of the author nor the names of its contributors may be
used to endorse or promote products derived from this software without specific
prior written permission. 

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

***************************************************************************

%%The following software may be included in this product:
gstreamer 

You are receiving a copy of the GStreamer library in object code in the 
JavaFX runtime or JavaFX SDK. A
copy of the Oracle modified GStreamer library in source code is located 
at http://oss.oracle.com/projects/gstreamer-mods/ . The terms of the 
Oracle license do NOT apply to the GStreamer program; it is licensed under 
the following license, separately from the Oracle programs you receive. If 
you do not wish to install this program, you may not wish to install 
the JavaFX Runtime or JavaFX SDK.

Use of any of this software is governed by the terms of the license below:

GNU LESSER GENERAL PUBLIC LICENSE Version 2.1, February 1999 
Copyright (C) 1991, 1999 Free Software Foundation, Inc. 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA Everyone is permitted to copy and distribute verbatim copies of this license document, but changing it is not allowed. 
[This is the first released version of the Lesser GPL. It also counts as the successor of the GNU Library Public License, version 2, hence the version number 2.1.] Preamble The licenses for most software are designed to take away your freedom to share and 
change it. By contrast, the GNU General Public Licenses are intended to guarantee 
your freedom to share and change free software--to make sure the software is free 
for all its users. 
This license, the Lesser General Public License, applies to some specially 
designated software packages--typically libraries--of the Free Software Foundation 
and other authors who decide to use it. You can use it too, but we suggest you first 
think carefully about whether this license or the ordinary General Public License is 
the better strategy to use in any particular case, based on the explanations below. 
When we speak of free software, we are referring to freedom of use, not price. Our 
General Public Licenses are designed to make sure that you have the freedom to 
distribute copies of free software (and charge for this service if you wish); that 
you receive source code or can get it if you want it; that you can change the 
software and use pieces of it in new free programs; and that you are informed that 
you can do these things. 
To protect your rights, we need to make restrictions that forbid distributors to 
deny you these rights or to ask you to surrender these rights. These restrictions 
translate to certain responsibilities for you if you distribute copies of the 
library or if you modify it. 
For example, if you distribute copies of the library, whether gratis or for a fee, 
you must give the recipients all the rights that we gave you. You must make sure 
that they, too, receive or can get the source code. If you link other code with the 
library, you must provide complete object files to the recipients, so that they can 
relink them with the library after making changes to the library and recompiling it. 
And you must show them these terms so they know their rights. 
We protect your rights with a two-step method: (1) we copyright the library, and (2) 
we offer you this license, which gives you legal permission to copy, distribute 
and/or modify the library. 
To protect each distributor, we want to make it very clear that there is no warranty 
for the free library. Also, if the library is modified by someone else and passed 
on, the recipients should know that what they have is not the original version, so 
that the original author's reputation will not be affected by problems that might be 
introduced by others. 
Finally, software patents pose a constant threat to the existence of any free 
program. We wish to make sure that a company cannot effectively restrict the users 
of a free program by obtaining a restrictive license from a patent holder. 
Therefore, we insist that any patent license obtained for a version of the library 
must be consistent with the full freedom of use specified in this license. 
Most GNU software, including some libraries, is covered by the ordinary GNU General 
Public License. This license, the GNU Lesser General Public License, applies to 
certain designated libraries, and is quite different from the ordinary General 
Public License. We use this license for certain libraries in order to permit linking 
those libraries into non-free programs. 
When a program is linked with a library, whether statically or using a shared 
library, the combination of the two is legally speaking a combined work, a 
derivative of the original library. The ordinary General Public License therefore 
permits such linking only if the entire combination fits its criteria of freedom. 
The Lesser General Public License permits more lax criteria for linking other code 
with the library. 
We call this license the "Lesser" General Public License because it does Less to 
protect the user's freedom than the ordinary General Public License. It also 
provides other free software developers Less of an advantage over competing non-free 
programs. These disadvantages are the reason we use the ordinary General Public 
License for many libraries. However, the Lesser license provides advantages in 
certain special circumstances. 
For example, on rare occasions, there may be a special need to encourage the widest 
possible use of a certain library, so that it becomes a de-facto standard. To 
achieve this, non-free programs must be allowed to use the library. A more frequent 
case is that a free library does the same job as widely used non-free libraries. In 
this case, there is little to gain by limiting the free library to free software 
only, so we use the Lesser General Public License. 
In other cases, permission to use a particular library in non-free programs enables 
a greater number of people to use a large body of free software. For example, 
permission to use the GNU C Library in non-free programs enables many more people to 
use the whole GNU operating system, as well as its variant, the GNU/Linux operating 
system. 
Although the Lesser General Public License is Less protective of the users' freedom, 
it does ensure that the user of a program that is linked with the Library has the 
freedom and the wherewithal to run that program using a modified version of the 
Library. 
The precise terms and conditions for copying, distribution and modification follow. 
Pay close attention to the difference between a "work based on the library" and a 
"work that uses the library". The former contains code derived from the library, 
whereas the latter must be combined with the library in order to run. 
TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION 0. This License Agreement applies to any software library or other program which 
contains a notice placed by the copyright holder or other authorized party saying it 
may be distributed under the terms of this Lesser General Public License (also 
called "this License"). Each licensee is addressed as "you". 
A "library" means a collection of software functions and/or data prepared so as to 
be conveniently linked with application programs (which use some of those functions 
and data) to form executables. 
The "Library", below, refers to any such software library or work which has been 
distributed under these terms. A "work based on the Library" means either the 
Library or any derivative work under copyright law: that is to say, a work 
containing the Library or a portion of it, either verbatim or with modifications 
and/or translated straightforwardly into another language. (Hereinafter, translation 
is included without limitation in the term "modification".) 
"Source code" for a work means the preferred form of the work for making 
modifications to it. For a library, complete source code means all the source code 
for all modules it contains, plus any associated interface definition files, plus 
the scripts used to control compilation and installation of the library. 
Activities other than copying, distribution and modification are not covered by this 
License; they are outside its scope. The act of running a program using the Library 
is not restricted, and output from such a program is covered only if its contents 
constitute a work based on the Library (independent of the use of the Library in a 
tool for writing it). Whether that is true depends on what the Library does and what 
the program that uses the Library does. 
1. You may copy and distribute verbatim copies of the Library's complete source code 
as you receive it, in any medium, provided that you conspicuously and appropriately 
publish on each copy an appropriate copyright notice and disclaimer of warranty; 
keep intact all the notices that refer to this License and to the absence of any 
warranty; and distribute a copy of this License along with the Library. 
You may charge a fee for the physical act of transferring a copy, and you may at 
your option offer warranty protection in exchange for a fee. 
2. You may modify your copy or copies of the Library or any portion of it, thus 
forming a work based on the Library, and copy and distribute such modifications or 
work under the terms of Section 1 above, provided that you also meet all of these 
conditions: 
a) The modified work must itself be a software library. b) You must cause the files modified to carry prominent notices stating that you 
changed the files and the date of any change. c) You must cause the whole of the work to be licensed at no charge to all third 
parties under the terms of this License. d) If a facility in the modified Library refers to a function or a table of data to 
be supplied by an application program that uses the facility, other than as an 
argument passed when the facility is invoked, then you must make a good faith effort 
to ensure that, in the event an application does not supply such function or table, 
the facility still operates, and performs whatever part of its purpose remains 
meaningful. (For example, a function in a library to compute square roots has a purpose that is 
entirely well-defined independent of the application. Therefore, Subsection 2d 
requires that any application-supplied function or table used by this function must 
be optional: if the application does not supply it, the square root function must 
still compute square roots.) 
These requirements apply to the modified work as a whole. If identifiable sections 
of that work are not derived from the Library, and can be reasonably considered 
independent and separate works in themselves, then this License, and its terms, do 
not apply to those sections when you distribute them as separate works. But when you 
distribute the same sections as part of a whole which is a work based on the 
Library, the distribution of the whole must be on the terms of this License, whose 
permissions for other licensees extend to the entire whole, and thus to each and 
every part regardless of who wrote it. Thus, it is not the intent of this section to claim rights or contest your rights to 
work written entirely by you; rather, the intent is to exercise the right to control 
the distribution of derivative or collective works based on the Library. 
In addition, mere aggregation of another work not based on the Library with the 
Library (or with a work based on the Library) on a volume of a storage or 
distribution medium does not bring the other work under the scope of this License. 
3. You may opt to apply the terms of the ordinary GNU General Public License instead 
of this License to a given copy of the Library. To do this, you must alter all the 
notices that refer to this License, so that they refer to the ordinary GNU General 
Public License, version 2, instead of to this License. (If a newer version than 
version 2 of the ordinary GNU General Public License has appeared, then you can 
specify that version instead if you wish.) Do not make any other change in these 
notices. 
Once this change is made in a given copy, it is irreversible for that copy, so the 
ordinary GNU General Public License applies to all subsequent copies and derivative 
works made from that copy. 
This option is useful when you wish to copy part of the code of the Library into a 
program that is not a library. 
4. You may copy and distribute the Library (or a portion or derivative of it, under 
Section 2) in object code or executable form under the terms of Sections 1 and 2 
above provided that you accompany it with the complete corresponding machine- 
readable source code, which must be distributed under the terms of Sections 1 and 2 
above on a medium customarily used for software interchange. 
If distribution of object code is made by offering access to copy from a designated 
place, then offering equivalent access to copy the source code from the same place 
satisfies the requirement to distribute the source code, even though third parties 
are not compelled to copy the source along with the object code. 
5. A program that contains no derivative of any portion of the Library, but is 
designed to work with the Library by being compiled or linked with it, is called a 
"work that uses the Library". Such a work, in isolation, is not a derivative work of 
the Library, and therefore falls outside the scope of this License. 
However, linking a "work that uses the Library" with the Library creates an 
executable that is a derivative of the Library (because it contains portions of the 
Library), rather than a "work that uses the library". The executable is therefore 
covered by this License. Section 6 states terms for distribution of such 
executables. 
When a "work that uses the Library" uses material from a header file that is part of 
the Library, the object code for the work may be a derivative work of the Library 
even though the source code is not. Whether this is true is especially significant 
if the work can be linked without the Library, or if the work is itself a library. 
The threshold for this to be true is not precisely defined by law. 
If such an object file uses only numerical parameters, data structure layouts and 
accessors, and small macros and small inline functions (ten lines or less in 
length), then the use of the object file is unrestricted, regardless of whether it 
is legally a derivative work. (Executables containing this object code plus portions 
of the Library will still fall under Section 6.) 
Otherwise, if the work is a derivative of the Library, you may distribute the object 
code for the work under the terms of Section 6. Any executables containing that work 
also fall under Section 6, whether or not they are linked directly with the Library 
itself. 
6. As an exception to the Sections above, you may also combine or link a "work that 
uses the Library" with the Library to produce a work containing portions of the 
Library, and distribute that work under terms of your choice, provided that the 
terms permit modification of the work for the customer's own use and reverse 
engineering for debugging such modifications. 
You must give prominent notice with each copy of the work that the Library is used 
in it and that the Library and its use are covered by this License. You must supply 
a copy of this License. If the work during execution displays copyright notices, you 
must include the copyright notice for the Library among them, as well as a reference 
directing the user to the copy of this License. Also, you must do one of these 
things: 
a) Accompany the work with the complete corresponding machine-readable source code 
for the Library including whatever changes were used in the work (which must be 
distributed under Sections 1 and 2 above); and, if the work is an executable linked 
with the Library, with the complete machine-readable "work that uses the Library", 
as object code and/or source code, so that the user can modify the Library and then 
relink to produce a modified executable containing the modified Library. (It is 
understood that the user who changes the contents of definitions files in the 
Library will not necessarily be able to recompile the application to use the 
modified definitions.) b) Use a suitable shared library mechanism for linking with the Library. A suitable 
mechanism is one that (1) uses at run time a copy of the library already present on 
the user's computer system, rather than copying library functions into the 
executable, and (2) will operate properly with a modified version of the library, if 
the user installs one, as long as the modified version is interface-compatible with 
the version that the work was made with. c) Accompany the work with a written offer, valid for at least three years, to give 
the same user the materials specified in Subsection 6a, above, for a charge no more 
than the cost of performing this distribution. d) If distribution of the work is made by offering access to copy from a designated 
place, offer equivalent access to copy the above specified materials from the same 
place. e) Verify that the user has already received a copy of these materials or that you 
have already sent this user a copy. For an executable, the required form of the "work that uses the Library" must 
include any data and utility programs needed for reproducing the executable from it. 
However, as a special exception, the materials to be distributed need not include 
anything that is normally distributed (in either source or binary form) with the 
major components (compiler, kernel, and so on) of the operating system on which the 
executable runs, unless that component itself accompanies the executable. 
It may happen that this requirement contradicts the license restrictions of other 
proprietary libraries that do not normally accompany the operating system. Such a 
contradiction means you cannot use both them and the Library together in an 
executable that you distribute. 
7. You may place library facilities that are a work based on the Library side-by- 
side in a single library together with other library facilities not covered by this 
License, and distribute such a combined library, provided that the separate 
distribution of the work based on the Library and of the other library facilities is 
otherwise permitted, and provided that you do these two things: 
a) Accompany the combined library with a copy of the same work based on the Library, uncombined with any other library facilities. This must be distributed under the terms of the Sections above. b) Give prominent notice with the combined library of the fact that part of it is a work based on the Library, and explaining where to find the accompanying uncombined form of the same work. 8. You may not copy, modify, sublicense, link with, or distribute the Library except as expressly provided under this License. Any attempt otherwise to copy, modify, sublicense, link with, or distribute the Library is void, and will automatically terminate your rights under this License. However, parties who have received copies, or rights, from you under this License will not have their licenses terminated so long as such parties remain in full compliance. 
9. You are not required to accept this License, since you have not signed it. However, nothing else grants you permission to modify or distribute the Library or its derivative works. These actions are prohibited by law if you do not accept this License. Therefore, by modifying or distributing the Library (or any work based on the Library), you indicate your acceptance of this License to do so, and all its terms and conditions for copying, distributing or modifying the Library or works based on it. 
10. Each time you redistribute the Library (or any work based on the Library), the recipient automatically receives a license from the original licensor to copy, distribute, link with or modify the Library subject to these terms and conditions. You may not impose any further restrictions on the recipients' exercise of the rights granted herein. You are not responsible for enforcing compliance by third parties with this License. 
11. If, as a consequence of a court judgment or allegation of patent infringement or for any other reason (not limited to patent issues), conditions are imposed on you (whether by court order, agreement or otherwise) that contradict the conditions of this License, they do not excuse you from the conditions of this License. If you cannot distribute so as to satisfy simultaneously your obligations under this License and any other pertinent obligations, then as a consequence you may not distribute the Library at all. For example, if a patent license would not permit royalty-free redistribution of the Library by all those who receive copies directly or indirectly through you, then the only way you could satisfy both it and this License would be to refrain entirely from distribution of the Library. 
If any portion of this section is held invalid or unenforceable under any particular circumstance, the balance of the section is intended to apply, and the section as a whole is intended to apply in other circumstances. 
It is not the purpose of this section to induce you to infringe any patents or other property right claims or to contest validity of any such claims; this section has the sole purpose of protecting the integrity of the free software distribution system which is implemented by public license practices. Many people have made generous contributions to the wide range of software distributed through that system in reliance on consistent application of that system; it is up to the author/donor to decide if he or she is willing to distribute software through any other system and a licensee cannot impose that choice. This section is intended to make thoroughly clear what is believed to be a consequence of the rest of this License. 
12. If the distribution and/or use of the Library is restricted in certain countries either by patents or by copyrighted interfaces, the original copyright holder who places the Library under this License may add an explicit geographical distribution limitation excluding those countries, so that distribution is permitted only in or among countries not thus excluded. In such case, this License incorporates the limitation as if written in the body of this License. 
13. The Free Software Foundation may publish revised and/or new versions of the Lesser General Public License from time to time. Such new versions will be similar in spirit to the present version, but may differ in detail to address new problems or concerns. 
Each version is given a distinguishing version number. If the Library specifies a version number of this License which applies to it and "any later version", you have the option of following the terms and conditions either of that version or of any later version published by the Free Software Foundation. If the Library does not specify a license version number, you may choose any version ever published by the 
Free Software Foundation. 
14. If you wish to incorporate parts of the Library into other free programs whose distribution conditions are incompatible with these, write to the author to ask for permission. For software which is copyrighted by the Free Software Foundation, write to the Free Software Foundation; we sometimes make exceptions for this. Our decision will be guided by the two goals of preserving the free status of all derivatives of our free software and of promoting the sharing and reuse of software generally. 
NO WARRANTY 
15. BECAUSE THE LIBRARY IS LICENSED FREE OF CHARGE, THERE IS NO WARRANTY FOR THE LIBRARY, TO THE EXTENT PERMITTED BY APPLICABLE LAW. EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR OTHER PARTIES PROVIDE THE LIBRARY "AS IS" WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE LIBRARY IS WITH 
YOU. SHOULD THE LIBRARY PROVE DEFECTIVE, YOU ASSUME THE COST OF ALL NECESSARY SERVICING, REPAIR OR CORRECTION. 
16. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY AND/OR REDISTRIBUTE THE LIBRARY AS PERMITTED ABOVE, BE LIABLE TO YOU FOR DAMAGES, INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OR INABILITY TO USE THE LIBRARY (INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR DATA BEING RENDERED INACCURATE 
OR LOSSES SUSTAINED BY YOU OR THIRD PARTIES OR A FAILURE OF THE LIBRARY TO OPERATE WITH ANY OTHER SOFTWARE), EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES. END OF TERMS AND CONDITIONS How to Apply These Terms to Your New Libraries If you develop a new library, and you want it to be of the greatest possible use to the public, we recommend making it free software that everyone can redistribute and change. You can do so by permitting redistribution under these terms (or, alternatively, under the terms of the ordinary General Public License). 
To apply these terms, attach the following notices to the library. It is safest to attach them to the start of each source file to most effectively convey the exclusion of warranty; and each file should have at least the "copyright" line and a pointer to where the full notice is found. one line to give the library's name and an idea of what it does. Copyright (C) year name of author 
This library is free software; you can redistribute it and/or modify it under the terms of the GNU Lesser General Public License as published by the Free Software Foundation; either version 2.1 of the License, or (at your option) any later version. 
This library is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more details. 
You should have received a copy of the GNU Lesser General Public License along with this library; if not, write to the Free Software Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA Also add information on how to contact you by electronic and paper mail. 
You should also get your employer (if you work as a programmer) or your school, if any, to sign a "copyright disclaimer" for the library, if necessary. Here is a sample; alter the names: 
Yoyodyne, Inc., hereby disclaims all copyright interest in the library `Frob' (a library for tweaking knobs) written by James Random Hacker. 
signature of Ty Coon, 1 April 1990 Ty Coon, President of Vice That's all there is to it!

***************************************************************************

%%The following software may be included in this product:
JFXtras Core v 0.5

Use of any of this software is governed by the terms of the license below:
Copyright (c) 2008-2009, JFXtras Group
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:
1. Redistributions of source code must retain the above copyright notice,
   this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution.
3. Neither the name of JFXtras nor the names of its contributors may be used
   to endorse or promote products derived from this software without
   specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE

***************************************************************************

%%The following software may be included in this product:
Webkit

You are receiving a copy of the WebKit library in object code in the 
JavaFX runtime or JavaFX SDK.
A copy of the Oracle modified WebKit library in source code is located
at http://oss.oracle.com/projects/webkit-java-mods/ . The terms of the 
Oracle license do NOT apply to the WebKit program; it is licensed under 
the following license separately from the Oracle programs you receive. 
If you do not wish to install this program, you may not wish to install 
the JavaFX runtime or JavaFX SDK.

Use of any of this software is governed by the terms of the license below:

GNU LIBRARY GENERAL PUBLIC LICENSE
Version 2, June 1991 
Copyright (C) 1991 Free Software Foundation, Inc.
51 Franklin St, Fifth Floor, Boston, MA  02110-1301, USA
Everyone is permitted to copy and distribute verbatim copies
of this license document, but changing it is not allowed.

[This is the first released version of the library GPL.  It is
 numbered 2 because it goes with version 2 of the ordinary GPL.]
Preamble
The licenses for most software are designed to take away your freedom to share and change it. By contrast, the GNU General Public Licenses are intended to guarantee your freedom to share and change free software--to make sure the software is free for all its users. 
This license, the Library General Public License, applies to some specially designated Free Software Foundation software, and to any other libraries whose authors decide to use it. You can use it for your libraries, too. 
When we speak of free software, we are referring to freedom, not price. Our General Public Licenses are designed to make sure that you have the freedom to distribute copies of free software (and charge for this service if you wish), that you receive source code or can get it if you want it, that you can change the software or use pieces of it in new free programs; and that you know you can do these things. 
To protect your rights, we need to make restrictions that forbid anyone to deny you these rights or to ask you to surrender the rights. These restrictions translate to certain responsibilities for you if you distribute copies of the library, or if you modify it. 
For example, if you distribute copies of the library, whether gratis or for a fee, you must give the recipients all the rights that we gave you. You must make sure that they, too, receive or can get the source code. If you link a program with the library, you must provide complete object files to the recipients so that they can relink them with the library, after making changes to the library and recompiling it. And you must show them these terms so they know their rights. 
Our method of protecting your rights has two steps: (1) copyright the library, and (2) offer you this license which gives you legal permission to copy, distribute and/or modify the library. 
Also, for each distributor's protection, we want to make certain that everyone understands that there is no warranty for this free library. If the library is modified by someone else and passed on, we want its recipients to know that what they have is not the original version, so that any problems introduced by others will not reflect on the original authors' reputations. 
Finally, any free program is threatened constantly by software patents. We wish to avoid the danger that companies distributing free software will individually obtain patent licenses, thus in effect transforming the program into proprietary software. To prevent this, we have made it clear that any patent must be licensed for everyone's free use or not licensed at all. 
Most GNU software, including some libraries, is covered by the ordinary GNU General Public License, which was designed for utility programs. This license, the GNU Library General Public License, applies to certain designated libraries. This license is quite different from the ordinary one; be sure to read it in full, and don't assume that anything in it is the same as in the ordinary license. 
The reason we have a separate public license for some libraries is that they blur the distinction we usually make between modifying or adding to a program and simply using it. Linking a program with a library, without changing the library, is in some sense simply using the library, and is analogous to running a utility program or application program. However, in a textual and legal sense, the linked executable is a combined work, a derivative of the original library, and the ordinary General Public License treats it as such. 
Because of this blurred distinction, using the ordinary General Public License for libraries did not effectively promote software sharing, because most developers did not use the libraries. We concluded that weaker conditions might promote sharing better. 
However, unrestricted linking of non-free programs would deprive the users of those programs of all benefit from the free status of the libraries themselves. This Library General Public License is intended to permit developers of non-free programs to use free libraries, while preserving your freedom as a user of such programs to change the free libraries that are incorporated in them. (We have not seen how to achieve this as regards changes in header files, but we have achieved it as regards changes in the actual functions of the Library.) The hope is that this will lead to faster development of free libraries. 
The precise terms and conditions for copying, distribution and modification follow. Pay close attention to the difference between a "work based on the library" and a "work that uses the library". The former contains code derived from the library, while the latter only works together with the library. 
Note that it is possible for a library to be covered by the ordinary General Public License rather than by this special one. 
TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION
0. This License Agreement applies to any software library which contains a notice placed by the copyright holder or other authorized party saying it may be distributed under the terms of this Library General Public License (also called "this License"). Each licensee is addressed as "you". 
A "library" means a collection of software functions and/or data prepared so as to be conveniently linked with application programs (which use some of those functions and data) to form executables. 
The "Library", below, refers to any such software library or work which has been distributed under these terms. A "work based on the Library" means either the Library or any derivative work under copyright law: that is to say, a work containing the Library or a portion of it, either verbatim or with modifications and/or translated straightforwardly into another language. (Hereinafter, translation is included without limitation in the term "modification".) 
"Source code" for a work means the preferred form of the work for making modifications to it. For a library, complete source code means all the source code for all modules it contains, plus any associated interface definition files, plus the scripts used to control compilation and installation of the library. 
Activities other than copying, distribution and modification are not covered by this License; they are outside its scope. The act of running a program using the Library is not restricted, and output from such a program is covered only if its contents constitute a work based on the Library (independent of the use of the Library in a tool for writing it). Whether that is true depends on what the Library does and what the program that uses the Library does. 
1. You may copy and distribute verbatim copies of the Library's complete source code as you receive it, in any medium, provided that you conspicuously and appropriately publish on each copy an appropriate copyright notice and disclaimer of warranty; keep intact all the notices that refer to this License and to the absence of any warranty; and distribute a copy of this License along with the Library. 
You may charge a fee for the physical act of transferring a copy, and you may at your option offer warranty protection in exchange for a fee. 
2. You may modify your copy or copies of the Library or any portion of it, thus forming a work based on the Library, and copy and distribute such modifications or work under the terms of Section 1 above, provided that you also meet all of these conditions: 
a) The modified work must itself be a software library. 
b) You must cause the files modified to carry prominent notices stating that you changed the files and the date of any change. 
c) You must cause the whole of the work to be licensed at no charge to all third parties under the terms of this License. 
d) If a facility in the modified Library refers to a function or a table of data to be supplied by an application program that uses the facility, other than as an argument passed when the facility is invoked, then you must make a good faith effort to ensure that, in the event an application does not supply such function or table, the facility still operates, and performs whatever part of its purpose remains meaningful. (For example, a function in a library to compute square roots has a purpose that is entirely well-defined independent of the application. Therefore, Subsection 2d requires that any application-supplied function or table used by this function must be optional: if the application does not supply it, the square root function must still compute square roots.) 
These requirements apply to the modified work as a whole. If identifiable sections of that work are not derived from the Library, and can be reasonably considered independent and separate works in themselves, then this License, and its terms, do not apply to those sections when you distribute them as separate works. But when you distribute the same sections as part of a whole which is a work based on the Library, the distribution of the whole must be on the terms of this License, whose permissions for other licensees extend to the entire whole, and thus to each and every part regardless of who wrote it. 
Thus, it is not the intent of this section to claim rights or contest your rights to work written entirely by you; rather, the intent is to exercise the right to control the distribution of derivative or collective works based on the Library. 
In addition, mere aggregation of another work not based on the Library with the Library (or with a work based on the Library) on a volume of a storage or distribution medium does not bring the other work under the scope of this License. 
3. You may opt to apply the terms of the ordinary GNU General Public License instead of this License to a given copy of the Library. To do this, you must alter all the notices that refer to this License, so that they refer to the ordinary GNU General Public License, version 2, instead of to this License. (If a newer version than version 2 of the ordinary GNU General Public License has appeared, then you can specify that version instead if you wish.) Do not make any other change in these notices. 
Once this change is made in a given copy, it is irreversible for that copy, so the ordinary GNU General Public License applies to all subsequent copies and derivative works made from that copy. 
This option is useful when you wish to copy part of the code of the Library into a program that is not a library. 
4. You may copy and distribute the Library (or a portion or derivative of it, under Section 2) in object code or executable form under the terms of Sections 1 and 2 above provided that you accompany it with the complete corresponding machine-readable source code, which must be distributed under the terms of Sections 1 and 2 above on a medium customarily used for software interchange. 
If distribution of object code is made by offering access to copy from a designated place, then offering equivalent access to copy the source code from the same place satisfies the requirement to distribute the source code, even though third parties are not compelled to copy the source along with the object code. 
5. A program that contains no derivative of any portion of the Library, but is designed to work with the Library by being compiled or linked with it, is called a "work that uses the Library". Such a work, in isolation, is not a derivative work of the Library, and therefore falls outside the scope of this License. 
However, linking a "work that uses the Library" with the Library creates an executable that is a derivative of the Library (because it contains portions of the Library), rather than a "work that uses the library". The executable is therefore covered by this License. Section 6 states terms for distribution of such executables. 
When a "work that uses the Library" uses material from a header file that is part of the Library, the object code for the work may be a derivative work of the Library even though the source code is not. Whether this is true is especially significant if the work can be linked without the Library, or if the work is itself a library. The threshold for this to be true is not precisely defined by law. 
If such an object file uses only numerical parameters, data structure layouts and accessors, and small macros and small inline functions (ten lines or less in length), then the use of the object file is unrestricted, regardless of whether it is legally a derivative work. (Executables containing this object code plus portions of the Library will still fall under Section 6.) 
Otherwise, if the work is a derivative of the Library, you may distribute the object code for the work under the terms of Section 6. Any executables containing that work also fall under Section 6, whether or not they are linked directly with the Library itself. 
6. As an exception to the Sections above, you may also compile or link a "work that uses the Library" with the Library to produce a work containing portions of the Library, and distribute that work under terms of your choice, provided that the terms permit modification of the work for the customer's own use and reverse engineering for debugging such modifications. 
You must give prominent notice with each copy of the work that the Library is used in it and that the Library and its use are covered by this License. You must supply a copy of this License. If the work during execution displays copyright notices, you must include the copyright notice for the Library among them, as well as a reference directing the user to the copy of this License. Also, you must do one of these things: 
a) Accompany the work with the complete corresponding machine-readable source code for the Library including whatever changes were used in the work (which must be distributed under Sections 1 and 2 above); and, if the work is an executable linked with the Library, with the complete machine-readable "work that uses the Library", as object code and/or source code, so that the user can modify the Library and then relink to produce a modified executable containing the modified Library. (It is understood that the user who changes the contents of definitions files in the Library will not necessarily be able to recompile the application to use the modified definitions.) 
b) Accompany the work with a written offer, valid for at least three years, to give the same user the materials specified in Subsection 6a, above, for a charge no more than the cost of performing this distribution. 
c) If distribution of the work is made by offering access to copy from a designated place, offer equivalent access to copy the above specified materials from the same place. 
d) Verify that the user has already received a copy of these materials or that you have already sent this user a copy. 
For an executable, the required form of the "work that uses the Library" must include any data and utility programs needed for reproducing the executable from it. However, as a special exception, the source code distributed need not include anything that is normally distributed (in either source or binary form) with the major components (compiler, kernel, and so on) of the operating system on which the executable runs, unless that component itself accompanies the executable. 
It may happen that this requirement contradicts the license restrictions of other proprietary libraries that do not normally accompany the operating system. Such a contradiction means you cannot use both them and the Library together in an executable that you distribute. 
7. You may place library facilities that are a work based on the Library side-by-side in a single library together with other library facilities not covered by this License, and distribute such a combined library, provided that the separate distribution of the work based on the Library and of the other library facilities is otherwise permitted, and provided that you do these two things: 
a) Accompany the combined library with a copy of the same work based on the Library, uncombined with any other library facilities. This must be distributed under the terms of the Sections above. 
b) Give prominent notice with the combined library of the fact that part of it is a work based on the Library, and explaining where to find the accompanying uncombined form of the same work. 
8. You may not copy, modify, sublicense, link with, or distribute the Library except as expressly provided under this License. Any attempt otherwise to copy, modify, sublicense, link with, or distribute the Library is void, and will automatically terminate your rights under this License. However, parties who have received copies, or rights, from you under this License will not have their licenses terminated so long as such parties remain in full compliance. 
9. You are not required to accept this License, since you have not signed it. However, nothing else grants you permission to modify or distribute the Library or its derivative works. These actions are prohibited by law if you do not accept this License. Therefore, by modifying or distributing the Library (or any work based on the Library), you indicate your acceptance of this License to do so, and all its terms and conditions for copying, distributing or modifying the Library or works based on it. 
10. Each time you redistribute the Library (or any work based on the Library), the recipient automatically receives a license from the original licensor to copy, distribute, link with or modify the Library subject to these terms and conditions. You may not impose any further restrictions on the recipients' exercise of the rights granted herein. You are not responsible for enforcing compliance by third parties to this License. 
11. If, as a consequence of a court judgment or allegation of patent infringement or for any other reason (not limited to patent issues), conditions are imposed on you (whether by court order, agreement or otherwise) that contradict the conditions of this License, they do not excuse you from the conditions of this License. If you cannot distribute so as to satisfy simultaneously your obligations under this License and any other pertinent obligations, then as a consequence you may not distribute the Library at all. For example, if a patent license would not permit royalty-free redistribution of the Library by all those who receive copies directly or indirectly through you, then the only way you could satisfy both it and this License would be to refrain entirely from distribution of the Library. 
If any portion of this section is held invalid or unenforceable under any particular circumstance, the balance of the section is intended to apply, and the section as a whole is intended to apply in other circumstances. 
It is not the purpose of this section to induce you to infringe any patents or other property right claims or to contest validity of any such claims; this section has the sole purpose of protecting the integrity of the free software distribution system which is implemented by public license practices. Many people have made generous contributions to the wide range of software distributed through that system in reliance on consistent application of that system; it is up to the author/donor to decide if he or she is willing to distribute software through any other system and a licensee cannot impose that choice. 
This section is intended to make thoroughly clear what is believed to be a consequence of the rest of this License. 
12. If the distribution and/or use of the Library is restricted in certain countries either by patents or by copyrighted interfaces, the original copyright holder who places the Library under this License may add an explicit geographical distribution limitation excluding those countries, so that distribution is permitted only in or among countries not thus excluded. In such case, this License incorporates the limitation as if written in the body of this License. 
13. The Free Software Foundation may publish revised and/or new versions of the Library General Public License from time to time. Such new versions will be similar in spirit to the present version, but may differ in detail to address new problems or concerns. 
Each version is given a distinguishing version number. If the Library specifies a version number of this License which applies to it and "any later version", you have the option of following the terms and conditions either of that version or of any later version published by the Free Software Foundation. If the Library does not specify a license version number, you may choose any version ever published by the Free Software Foundation. 
14. If you wish to incorporate parts of the Library into other free programs whose distribution conditions are incompatible with these, write to the author to ask for permission. For software which is copyrighted by the Free Software Foundation, write to the Free Software Foundation; we sometimes make exceptions for this. Our decision will be guided by the two goals of preserving the free status of all derivatives of our free software and of promoting the sharing and reuse of software generally. 
NO WARRANTY
15. BECAUSE THE LIBRARY IS LICENSED FREE OF CHARGE, THERE IS NO WARRANTY FOR THE LIBRARY, TO THE EXTENT PERMITTED BY APPLICABLE LAW. EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR OTHER PARTIES PROVIDE THE LIBRARY "AS IS" WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE LIBRARY IS WITH YOU. SHOULD THE LIBRARY PROVE DEFECTIVE, YOU ASSUME THE COST OF ALL NECESSARY SERVICING, REPAIR OR CORRECTION. 
16. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY AND/OR REDISTRIBUTE THE LIBRARY AS PERMITTED ABOVE, BE LIABLE TO YOU FOR DAMAGES, INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OR INABILITY TO USE THE LIBRARY (INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR DATA BEING RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD PARTIES OR A FAILURE OF THE LIBRARY TO OPERATE WITH ANY OTHER SOFTWARE), EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES. 



***************************************************************************

%%The following software may be included in this product:
zlib


/* zlib.h -- interface of the 'zlib' general purpose compression library
  version 1.2.2, October 3rd, 2004

  Copyright (C) 1995-2004 Jean-loup Gailly and Mark Adler

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.
  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.
  3. This notice may not be removed or altered from any source distribution.

  Jean-<NAME_EMAIL>
  <NAME_EMAIL>

*/

***************************************************************************

%%The following software may be included in this product:
libpng

This copy of the libpng notices is provided for your convenience.  In case of
any discrepancy between this copy and the notices in the file png.h that is
included in the libpng distribution, the latter shall prevail.

COPYRIGHT NOTICE, DISCLAIMER, and LICENSE:

If you modify libpng you may insert additional notices immediately following
this sentence.

This code is released under the libpng license.

libpng versions 1.2.6, August 15, 2004, through 1.5.0, January 6, 2011, are
Copyright (c) 2004, 2006-2010 Glenn Randers-Pehrson, and are
distributed according to the same disclaimer and license as libpng-1.2.5
with the following individual added to the list of Contributing Authors

   Cosmin Truta

libpng versions 1.0.7, July 1, 2000, through 1.2.5 - October 3, 2002, are
Copyright (c) 2000-2002 Glenn Randers-Pehrson, and are
distributed according to the same disclaimer and license as libpng-1.0.6
with the following individuals added to the list of Contributing Authors

   Simon-Pierre Cadieux
   Eric S. Raymond
   Gilles Vollant

and with the following additions to the disclaimer:

   There is no warranty against interference with your enjoyment of the
   library or against infringement.  There is no warranty that our
   efforts or the library will fulfill any of your particular purposes
   or needs.  This library is provided with all faults, and the entire
   risk of satisfactory quality, performance, accuracy, and effort is with
   the user.

libpng versions 0.97, January 1998, through 1.0.6, March 20, 2000, are
Copyright (c) 1998, 1999 Glenn Randers-Pehrson, and are
distributed according to the same disclaimer and license as libpng-0.96,
with the following individuals added to the list of Contributing Authors: <AUTHORS>
   Glenn Randers-Pehrson
   Willem van Schaik

libpng versions 0.89, June 1996, through 0.96, May 1997, are
Copyright (c) 1996, 1997 Andreas Dilger
Distributed according to the same disclaimer and license as libpng-0.88,
with the following individuals added to the list of Contributing Authors: <AUTHORS>
   Kevin Bracey
   Sam Bushell
   Magnus Holmgren
   Greg Roelofs
   Tom Tanner

libpng versions 0.5, May 1995, through 0.88, January 1996, are
Copyright (c) 1995, 1996 Guy Eric Schalnat, Group 42, Inc.

For the purposes of this copyright and license, "Contributing Authors"
is defined as the following set of individuals:

   Andreas Dilger
   Dave Martindale
   Guy Eric Schalnat
   Paul Schmidt
   Tim Wegner

The PNG Reference Library is supplied "AS IS".  The Contributing Authors
and Group 42, Inc. disclaim all warranties, expressed or implied,
including, without limitation, the warranties of merchantability and of
fitness for any purpose.  The Contributing Authors and Group 42, Inc.
assume no liability for direct, indirect, incidental, special, exemplary,
or consequential damages, which may result from the use of the PNG
Reference Library, even if advised of the possibility of such damage.

Permission is hereby granted to use, copy, modify, and distribute this
source code, or portions hereof, for any purpose, without fee, subject
to the following restrictions:

1. The origin of this source code must not be misrepresented.

2. Altered versions must be plainly marked as such and must not
   be misrepresented as being the original source.

3. This Copyright notice may not be removed or altered from any
   source or altered source distribution.

The Contributing Authors and Group 42, Inc. specifically permit, without
fee, and encourage the use of this source code as a component to
supporting the PNG file format in commercial products.  If you use this
source code in a product, acknowledgment is not required but would be
appreciated.


A "png_get_copyright" function is available, for convenient use in "about"
boxes and the like:

   printf("%s",png_get_copyright(NULL));

Also, the PNG logo (in PNG format, of course) is supplied in the
files "pngbar.png" and "pngbar.jpg (88x31) and "pngnow.png" (98x31).

Libpng is OSI Certified Open Source Software.  OSI Certified Open Source is a
certification mark of the Open Source Initiative.

Glenn Randers-Pehrson
glennrp at users.sourceforge.net
January 6, 2011

***************************************************************************

%%The following software may be included in this product:
libxml

The MIT License
Copyright (c) <year> <copyright holders>
Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:
The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.
THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

***************************************************************************

%%The following software may be included in this product:
libxslt

The MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
