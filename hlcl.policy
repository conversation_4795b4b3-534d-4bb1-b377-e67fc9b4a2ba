/* Copyright Hapag-Lloyd AG, 2018 */
/* DO NOT EDIT */

grant {
  permission java.awt.AWTPermission "accessClipboard";
  permission java.awt.AWTPermission "accessEventQueue";
  permission java.awt.AWTPermission "showWindowWithoutWarningBanner";
  permission java.awt.AWTPermission "listenToAllAWTEvents";
  permission java.awt.AWTPermission "readDisplayPixels";
  permission java.awt.AWTPermission "replaceKeyboardFocusManager";
  permission java.awt.AWTPermission "createRobot";

  /* Java Temp Directory  */
  permission java.io.FilePermission "${java.io.tmpdir}", "read";
  permission java.io.FilePermission "${java.io.tmpdir}", "write";
  permission java.io.FilePermission "${java.io.tmpdir}${/}-", "write";
  permission java.io.FilePermission "${java.io.tmpdir}${/}-", "read";
  permission java.io.FilePermission "${java.io.tmpdir}${/}-", "delete";

  /* FIS 2 Cache Directory  */
  permission java.io.FilePermission "${fis2.cache}", "read";
  permission java.io.FilePermission "${fis2.cache}", "write";
  permission java.io.FilePermission "${fis2.cache}${/}-", "write";
  permission java.io.FilePermission "${fis2.cache}${/}-", "read";
  permission java.io.FilePermission "${fis2.cache}${/}-", "delete";

  /* FIS 2 Data Exchange  */
  permission java.io.FilePermission "${fis2.xchange}", "read";
  permission java.io.FilePermission "${fis2.xchange}", "write";
  permission java.io.FilePermission "${fis2.xchange}${/}-", "write";
  permission java.io.FilePermission "${fis2.xchange}${/}-", "read";
  permission java.io.FilePermission "${fis2.xchange}${/}-", "delete";

  /* run tools */
  permission java.io.FilePermission "${fis2.browser}" , "execute";
  permission java.io.FilePermission "${fis2.mail}" , "execute";
  permission java.io.FilePermission "${fis2.texteditor}" , "execute";
  permission java.io.FilePermission "${fis2.spreadsheet}" , "execute";
  permission java.io.FilePermission "${fis2.presentation}" , "execute";

  permission java.io.FilePermission "<<ALL FILES>>", "read,execute";
  
  permission java.lang.reflect.ReflectPermission "suppressAccessChecks";

  permission java.lang.RuntimePermission "createClassLoader";
  permission java.lang.RuntimePermission "getClassLoader";
  permission java.lang.RuntimePermission "getStackTrace";
  permission java.lang.RuntimePermission "setContextClassLoader";
  permission java.lang.RuntimePermission "exitVM";
  permission java.lang.RuntimePermission "modifyThread";
  permission java.lang.RuntimePermission "modifyThreadGroup";
  permission java.lang.RuntimePermission "stopThread";
  permission java.lang.RuntimePermission "readFileDescriptor";
  permission java.lang.RuntimePermission "writeFileDescriptor";
  permission java.lang.RuntimePermission "queuePrintJob";
  permission java.lang.RuntimePermission "usePolicy";
  permission java.lang.RuntimePermission "getProtectionDomain";
  permission java.lang.RuntimePermission "setDefaultUncaughtExceptionHandler";
  permission java.lang.RuntimePermission "accessDeclaredMembers";
  permission java.lang.RuntimePermission "accessClassInPackage.sun.misc";
  permission java.lang.RuntimePermission "accessClassInPackage.sun.swing";  
  permission java.lang.RuntimePermission "getenv.windir";
  permission java.lang.RuntimePermission "getFileSystemAttributes"; 

  permission java.security.SecurityPermission "getPolicy";
  permission java.security.SecurityPermission "getProperty";

  permission java.io.SerializablePermission "enableSubclassImplementation";
  
  permission java.net.SocketPermission "${fis2.base.server}:${fis2.base.port}", "connect,resolve";
  permission java.net.SocketPermission "${fis2.log.server}:${fis2.log.port}", "connect,resolve";
  permission java.net.SocketPermission "${http.proxyHost}:${http.proxyPort}", "connect,resolve";
  permission java.net.SocketPermission "localhost:1024-", "accept,connect,resolve";
  permission java.net.SocketPermission "localhost:${fis2.ecf.port}", "listen,resolve";

  /* Microsoft Virtual Earth */
  permission java.net.SocketPermission "*", "connect,resolve";

  permission java.util.PropertyPermission "java.*", "read";
  permission java.util.PropertyPermission "os.name", "read";
  permission java.util.PropertyPermission "os.version", "read";
  permission java.util.PropertyPermission "os.arch", "read";
  permission java.util.PropertyPermission "file.*", "read";
  permission java.util.PropertyPermission "file.separator", "read";
  permission java.util.PropertyPermission "path.separator", "read";
  permission java.util.PropertyPermission "line.separator", "read";
  permission java.util.PropertyPermission "user.*", "read,write";
  permission java.util.PropertyPermission "fis2.*", "read";
  permission java.util.PropertyPermission "com.apple.*", "read";
  permission java.util.PropertyPermission "macosx.*", "read";
  permission java.util.PropertyPermission "sun.net.client.defaultConnectTimeout", "read,write";
  permission java.util.PropertyPermission "sun.net.client.defaultReadTimeout", "read,write";
  permission java.util.PropertyPermission "log-level", "read";
  permission java.util.PropertyPermission "jadice.*", "read,write";
  permission java.util.PropertyPermission "com.levigo.jadice.*", "read,write";
  permission java.util.PropertyPermission "derby.*", "read,write";
  permission java.util.PropertyPermission "http.*", "read,write";
  permission java.util.PropertyPermission "http.proxyID", "read,write";
  permission java.util.PropertyPermission "http.proxySet", "read,write";
  permission java.util.PropertyPermission "http.proxyHost", "read,write";
  permission java.util.PropertyPermission "http.proxyPort", "read,write";
  permission java.util.PropertyPermission "http.nonProxyHosts", "read,write"; 
  permission java.util.PropertyPermission "http.proxyUser", "read,write"; 
  permission java.util.PropertyPermission "http.proxyPassword", "read,write"; 

  /* JDIC */
  permission java.util.PropertyPermission "java.library.path", "read,write";
  permission java.util.PropertyPermission "java.ext.dirs", "read,write";
  permission java.util.PropertyPermission "sun.boot.library.path", "read";
  permission java.util.PropertyPermission "javawebstart.version", "read";
  permission java.util.PropertyPermission "org.jdesktop.jdic.browser.BrowserManager", "read";
  permission java.util.PropertyPermission "file.encoding", "read";
  permission java.lang.RuntimePermission  "loadLibrary.jdic";
  permission java.lang.RuntimePermission  "loadLibrary.tray";
  permission java.io.FilePermission       "${java.io.tmpdir}${/}-" , "execute";

  /* AXIS */ 
  permission java.io.FilePermission        "client-config.wsdd", "read, write"; 
  permission java.util.PropertyPermission "org.apache.commons.logging.LogFactory", "read"; 
  permission java.util.PropertyPermission "javax.xml.*", "read"; 
  permission java.util.PropertyPermission "java.protocol.handler.pkgs", "read,write"; 
  permission java.util.PropertyPermission "log4j.*", "read"; 
  permission java.io.FilePermission       "${java.home}${/}lib${/}jaxp.properties", "read"; 
  permission java.util.PropertyPermission "axis.*", "read"; 

  /* ERPELINO */
  permission java.util.PropertyPermission "sun.swing.*", "read";
  permission java.util.PropertyPermission "runnerURL", "read";
  permission java.util.PropertyPermission "masterLog", "read";
  permission java.util.PropertyPermission "org.apache.commons.logging.Log", "read,write";
  permission java.lang.RuntimePermission "setFactory";
  permission java.lang.RuntimePermission "shutdownHooks";
  permission javax.net.ssl.SSLPermission "setHostnameVerifier";
  permission java.awt.AWTPermission "setWindowAlwaysOnTop";
  permission java.net.SocketPermission "localhost:1-", "accept,connect,resolve";

  /* SWT */
  permission java.util.PropertyPermission "sun.arch.*", "read,write";
  permission java.util.PropertyPermission "swt.*", "read,write";
  permission java.util.PropertyPermission "org.eclipse.swt.*", "read,write";
  permission java.lang.RuntimePermission  "loadLibrary.*";
  
  /* MACRO ENABLEMENT */
  permission java.lang.RuntimePermission "accessClassInPackage.com.sun.xml.internal.bind.api";
  permission java.lang.RuntimePermission "accessClassInPackage.com.sun.xml.internal.ws.spi";
  permission java.lang.RuntimePermission "accessClassInPackage.sun.util.logging.resources";
  permission java.lang.RuntimePermission "accessClassInPackage.com.sun.xml.internal.bind.v2.runtime.reflect";
  permission javax.xml.ws.WebServicePermission "publishEndpoint";
  permission javax.management.MBeanServerPermission "createMBeanServer";
  permission javax.management.MBeanPermission "com.sun.xml.internal.ws.util.RuntimeVersion#-[com.sun.xml.internal.ws.util:type=RuntimeVersion]", "registerMBean";
  permission javax.management.MBeanPermission "com.sun.xml.internal.ws.transport.http.HttpDump#-[com.sun.xml.internal.ws.transport.http:type=HttpDump]", "registerMBean";
  permission java.util.PropertyPermission "dtm.debug", "read";
  permission java.util.PropertyPermission "com.sun.xml.internal.ws.api.streaming.XMLStreamWriterFactory.woodstox", "read";
  permission java.util.PropertyPermission "com.sun.xml.internal.ws.api.streaming.XMLStreamWriterFactory.noPool", "read";
  permission java.util.PropertyPermission "com.sun.xml.internal.ws.api.pipe.Fiber.serialize", "read";  
  permission java.lang.RuntimePermission "accessClassInPackage.com.sun.xml.internal.ws.fault"; 
  permission java.io.FilePermission "C:\\HLAG\\${/}-", "write";
  permission java.io.FilePermission "C:\\HLAG\\", "write";
  permission java.net.SocketPermission "localhost:7660-", "listen,resolve";  

  /* JAVA 8 STUFF */
  permission java.util.PropertyPermission "*", "read,write";
  permission java.util.logging.LoggingPermission "control";
  permission java.util.PropertyPermission "com.sun.xml.internal.ws.disableXmlSecurity", "read";
  permission java.util.PropertyPermission "com.sun.xml.internal.ws.monitoring.endpoint", "read";
  permission java.util.PropertyPermission "com.sun.xml.internal.ws.monitoring.client", "read";
  permission java.util.PropertyPermission "com.sun.xml.internal.ws.monitoring.typelibDebug", "read";
  permission java.util.PropertyPermission "com.sun.xml.internal.ws.monitoring.registrationDebug", "read";
  permission java.util.PropertyPermission "com.sun.xml.internal.ws.monitoring.runtimeDebug", "read";
  permission java.util.PropertyPermission "com.sun.xml.internal.ws.monitoring.maxUniqueEndpointRootNameRetries", "read";
  permission java.net.URLPermission "http:*", "*:*";
  permission java.net.URLPermission "https:*", "*:*";
  permission java.net.NetPermission "*";
  permission java.util.PropertyPermission "glassfish.version", "read";
  permission java.util.PropertyPermission "com.sun.metro.soap.*", "read";
  permission java.util.PropertyPermission "com.sun.xml.*", "read";
  permission java.lang.RuntimePermission "accessClassInPackage.com.sun.*";
  permission java.security.SecurityPermission "getProperty.*";
  permission java.security.SecurityPermission "putProviderProperty.SUN";

  /* JxBrowser */
  permission java.lang.RuntimePermission "getenv.LOCALAPPDATA";
  permission java.lang.RuntimePermission "preferences";
  permission java.net.SocketPermission "localhost:1100-", "listen,connect,resolve";  
  permission java.util.PropertyPermission "jxbrowser.*", "read";

  permission java.io.FilePermission "${user.home}${/}AppData${/}Local${/}JxBrowser", "read";
  permission java.io.FilePermission "${user.home}${/}AppData${/}Local${/}JxBrowser", "write"; 
  permission java.io.FilePermission "${user.home}${/}AppData${/}Local${/}JxBrowser", "delete";
  permission java.io.FilePermission "${user.home}${/}AppData${/}Local${/}JxBrowser${/}-", "read";
  permission java.io.FilePermission "${user.home}${/}AppData${/}Local${/}JxBrowser${/}-", "write";
  permission java.io.FilePermission "${user.home}${/}AppData${/}Local${/}JxBrowser${/}-", "delete";   
  
  permission java.io.FilePermission "${jxbrowser.chromium.dir}", "read";
  permission java.io.FilePermission "${jxbrowser.chromium.dir}", "write";
  permission java.io.FilePermission "${jxbrowser.chromium.dir}", "delete";
  permission java.io.FilePermission "${jxbrowser.chromium.dir}${/}-", "write";
  permission java.io.FilePermission "${jxbrowser.chromium.dir}${/}-", "read";  
  permission java.io.FilePermission "${jxbrowser.chromium.dir}${/}-", "delete";
  
  /* Teamsite access */
  permission java.io.FilePermission "//teamsites.hlag.com/-", "read, write"; 
  permission java.io.FilePermission "//teamsites.hlag.com", "read, write"; 
  permission java.io.FilePermission "\\teamsites.hlag.com\-", "read, write"; 
  permission java.io.FilePermission "\\teamsites.hlag.com", "read, write"; 
};

