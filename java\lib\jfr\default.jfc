<?xml version="1.0" encoding="UTF-8"?>

<!--
     Recommended way to edit .jfc files is to use Java Mission Control,
     see Window -> Flight Recorder Template Manager.
-->

<configuration version="1.0" name="Continuous" description="Low overhead configuration safe for continuous use in production environments, typically less than 1 % overhead." provider="Oracle">

  <producer uri="http://www.oracle.com/hotspot/jvm/" label="Oracle JDK">

    <control>

      <!--
           Contents of the control element is not read by the JVM, it's used
           by Java Mission Control to change settings that carry the control attribute.
      -->

      <selection name="gc-level" default="detailed" label="Garbage Collector">
        <option label="Off" name="off">off</option>
        <option label="Normal" name="detailed">normal</option>
        <option label="All" name="all">all</option>
      </selection>

      <condition name="gc-enabled-normal" true="true" false="false">
        <or>
          <test name="gc-level" operator="equal" value="normal"/>
          <test name="gc-level" operator="equal" value="all"/>
        </or>
      </condition>

      <condition name="gc-enabled-all" true="true" false="false">
        <test name="gc-level" operator="equal" value="all"/>
      </condition>

      <selection name="compiler-level" default="normal" label="Compiler">
        <option label="Off" name="off">off</option>
        <option label="Normal" name="normal">normal</option>
        <option label="Detailed" name="detailed">detailed</option>
        <option label="All" name="all">all</option>
      </selection>

      <condition name="compiler-enabled" true="false" false="true">
        <test name="compiler-level" operator="equal" value="off"/>
      </condition>

      <condition name="compiler-enabled-failure" true="true" false="false">
        <or>
          <test name="compiler-level" operator="equal" value="detailed"/>
          <test name="compiler-level" operator="equal" value="all"/>
        </or>
      </condition>

      <condition name="compiler-sweeper-threshold" true="0 ms" false="100 ms">
        <test name="compiler-level" operator="equal" value="all"/>
      </condition>

      <condition name="compiler-compilation-threshold" true="1000 ms">
        <test name="compiler-level" operator="equal" value="normal"/>
      </condition>

      <condition name="compiler-compilation-threshold" true="100 ms">
        <test name="compiler-level" operator="equal" value="detailed"/>
      </condition>

      <condition name="compiler-compilation-threshold" true="0 ms">
        <test name="compiler-level" operator="equal" value="all"/>
      </condition>

      <condition name="compiler-phase-threshold" true="60 s">
        <test name="compiler-level" operator="equal" value="normal"/>
      </condition>

      <condition name="compiler-phase-threshold" true="10 s">
        <test name="compiler-level" operator="equal" value="detailed"/>
      </condition>

      <condition name="compiler-phase-threshold" true="0 s">
        <test name="compiler-level" operator="equal" value="all"/>
      </condition>

      <selection name="method-sampling-interval" default="normal" label="Method Sampling">
        <option label="Off" name="off">999 d</option>
        <option label="Normal" name="normal">20 ms</option>
        <option label="Maximum" name="maximum">10 ms</option>
      </selection>

      <condition name="method-sampling-enabled" true="false" false="true">
        <test name="method-sampling-interval" operator="equal" value="999 d"/>
      </condition>

      <selection name="thread-dump-interval" default="normal" label="Thread Dump">
        <option label="Off" name="off">999 d</option>
        <option label="At least Once" name="normal">everyChunk</option>
        <option label="Every 60 s" name="everyMinute">60 s</option>
        <option label="Every 10 s" name="everyTenSecond">10 s</option>
        <option label="Every 1 s" name="everySecond">1 s</option>
      </selection>

      <condition name="thread-dump-enabled" true="false" false="true">
        <test name="thread-dump-interval" operator="equal" value="999 d"/>
      </condition>

      <selection name="exception-level" default="errors" label="Exceptions">
        <option label="Off" name="off">off</option>
        <option label="Errors Only" name="errors">errors</option>
        <option label="All Exceptions, including Errors" name="all">all</option>
      </selection>

      <condition name="enable-errors" true="true" false="false">
        <or>
          <test name="exception-level" operator="equal" value="errors"/>
          <test name="exception-level" operator="equal" value="all"/>
        </or>
      </condition>

      <condition name="enable-exceptions" true="true" false="false">
        <test name="exception-level" operator="equal" value="all"/>
      </condition>

      <text name="synchronization-threshold" label="Synchronization Threshold" contentType="timespan" minimum="0 s">20 ms</text>

      <text name="file-io-threshold" label="File I/O Threshold" contentType="timespan" minimum="0 s">20 ms</text>

      <text name="socket-io-threshold" label="Socket I/O Threshold" contentType="timespan" minimum="0 s">20 ms</text>

      <flag name="heap-statistics-enabled" label="Heap Statistics">false</flag>

      <flag name="class-loading-enabled" label="Class Loading">false</flag>

      <flag name="allocation-profiling-enabled" label="Allocation Profiling">false</flag>

    </control>

    <event path="java/statistics/thread_allocation">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="java/statistics/class_loading">
      <setting name="enabled">true</setting>
      <setting name="period">1000 ms</setting>
    </event>

    <event path="java/statistics/threads">
      <setting name="enabled">true</setting>
      <setting name="period">1000 ms</setting>
    </event>

    <event path="java/thread_start">
      <setting name="enabled">true</setting>
    </event>

    <event path="java/thread_end">
      <setting name="enabled">true</setting>
    </event>

    <event path="java/thread_sleep">
      <setting name="enabled">true</setting>
      <setting name="stackTrace">true</setting>
      <setting name="threshold" control="synchronization-threshold">20 ms</setting>
    </event>

    <event path="java/thread_park">
      <setting name="enabled">true</setting>
      <setting name="stackTrace">true</setting>
      <setting name="threshold" control="synchronization-threshold">20 ms</setting>
    </event>

    <event path="java/monitor_enter">
      <setting name="enabled">true</setting>
      <setting name="stackTrace">true</setting>
      <setting name="threshold" control="synchronization-threshold">20 ms</setting>
    </event>

    <event path="java/monitor_wait">
      <setting name="enabled">true</setting>
      <setting name="stackTrace">true</setting>
      <setting name="threshold" control="synchronization-threshold">20 ms</setting>
    </event>

    <event path="vm/class/load">
      <setting name="enabled" control="class-loading-enabled">false</setting>
      <setting name="stackTrace">true</setting>
      <setting name="threshold">0 ms</setting>
    </event>

    <event path="vm/class/unload">
      <setting name="enabled" control="class-loading-enabled">false</setting>
    </event>

    <event path="vm/info">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/initial_system_property">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/prof/execution_sample">
      <setting name="enabled" control="method-sampling-enabled">true</setting>
      <setting name="period" control="method-sampling-interval">20 ms</setting>
    </event>

    <event path="vm/prof/execution_sampling_info">
      <setting name="enabled">false</setting>
      <setting name="threshold">1 ms</setting>
    </event>

    <event path="vm/runtime/execute_vm_operation">
      <setting name="enabled">true</setting>
      <setting name="threshold">10 ms</setting>
    </event>

    <event path="vm/runtime/thread_dump">
      <setting name="enabled" control="thread-dump-enabled">true</setting>
      <setting name="period" control="thread-dump-interval">everyChunk</setting>
    </event>

    <event path="vm/flag/long">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/flag/ulong">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/flag/double">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/flag/boolean">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/flag/string">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/flag/long_changed">
      <setting name="enabled">true</setting>
    </event>

    <event path="vm/flag/ulong_changed">
      <setting name="enabled">true</setting>
    </event>

    <event path="vm/flag/double_changed">
      <setting name="enabled">true</setting>
    </event>

    <event path="vm/flag/boolean_changed">
      <setting name="enabled">true</setting>
    </event>

    <event path="vm/flag/string_changed">
      <setting name="enabled">true</setting>
    </event>

    <event path="vm/gc/detailed/object_count">
      <setting name="enabled" control="heap-statistics-enabled">false</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/gc/configuration/gc">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/gc/configuration/heap">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/gc/configuration/young_generation">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/gc/configuration/tlab">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/gc/configuration/survivor">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/gc/detailed/object_count_after_gc">
      <setting name="enabled">false</setting>
    </event>

    <event path="vm/gc/heap/summary">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
    </event>

    <event path="vm/gc/heap/ps_summary">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
    </event>

    <event path="vm/gc/heap/metaspace_summary">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
    </event>

    <event path="vm/gc/metaspace/gc_threshold">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
    </event>

    <event path="vm/gc/metaspace/allocation_failure">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="stackTrace">true</setting>
    </event>

    <event path="vm/gc/metaspace/out_of_memory">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="stackTrace">true</setting>
    </event>

    <event path="vm/gc/metaspace/chunk_free_list_summary">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
    </event>

    <event path="vm/gc/collector/garbage_collection">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="threshold">0 ms</setting>
    </event>

    <event path="vm/gc/collector/parold_garbage_collection">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="threshold">0 ms</setting>
    </event>

    <event path="vm/gc/collector/young_garbage_collection">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="threshold">0 ms</setting>
    </event>

    <event path="vm/gc/collector/old_garbage_collection">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="threshold">0 ms</setting>
    </event>

    <event path="vm/gc/collector/g1_garbage_collection">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="threshold">0 ms</setting>
    </event>

    <event path="vm/gc/phases/pause">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="threshold">0 ms</setting>
    </event>

    <event path="vm/gc/phases/pause_level_1">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="threshold">0 ms</setting>
    </event>

    <event path="vm/gc/phases/pause_level_2">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
      <setting name="threshold">0 ms</setting>
    </event>

    <event path="vm/gc/phases/pause_level_3">
      <setting name="enabled" control="gc-enabled-all">false</setting>
      <setting name="threshold">0 ms</setting>
    </event>

    <event path="vm/gc/reference/statistics">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
    </event>

    <event path="vm/gc/detailed/promotion_failed">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
    </event>

    <event path="vm/gc/detailed/evacuation_failed">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
    </event>

    <event path="vm/gc/detailed/evacuation_info">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
    </event>

    <event path="vm/gc/detailed/concurrent_mode_failure">
      <setting name="enabled" control="gc-enabled-normal">true</setting>
    </event>

    <event path="vm/gc/detailed/allocation_requiring_gc">
      <setting name="enabled" control="gc-enabled-all">false</setting>
      <setting name="stackTrace">true</setting>
    </event>

    <event path="vm/compiler/config">
      <setting name="enabled" control="compiler-enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/compiler/stats">
      <setting name="enabled" control="compiler-enabled">true</setting>
      <setting name="period">1000 ms</setting>
    </event>

    <event path="vm/compiler/compilation">
      <setting name="enabled" control="compiler-enabled">true</setting>
      <setting name="threshold" control="compiler-compilation-threshold">1000 ms</setting>
    </event>

    <event path="vm/compiler/phase">
      <setting name="enabled" control="compiler-enabled">true</setting>
      <setting name="threshold" control="compiler-phase-threshold">60 s</setting>
    </event>

    <event path="vm/compiler/failure">
      <setting name="enabled" control="compiler-enabled-failure">false</setting>
    </event>

    <event path="vm/code_sweeper/config">
      <setting name="enabled" control="compiler-enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/code_sweeper/stats">
      <setting name="enabled" control="compiler-enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/code_sweeper/sweep">
      <setting name="enabled" control="compiler-enabled">true</setting>
      <setting name="threshold" control="compiler-sweeper-threshold">100 ms</setting>
    </event>

    <event path="vm/code_cache/config">
      <setting name="enabled" control="compiler-enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/code_cache/stats">
      <setting name="enabled" control="compiler-enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="vm/code_cache/full">
      <setting name="enabled" control="compiler-enabled">true</setting>
    </event>

    <event path="os/information">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="os/processor/cpu_information">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="os/processor/context_switch_rate">
      <setting name="enabled" control="compiler-enabled">true</setting>
      <setting name="period">10 s</setting>
    </event>

    <event path="os/processor/cpu_load">
      <setting name="enabled">true</setting>
      <setting name="period">1000 ms</setting>
    </event>

    <event path="os/processor/cpu_tsc">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="os/system_process">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="os/initial_environment_variable">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="os/memory/physical_memory">
      <setting name="enabled">true</setting>
      <setting name="period">everyChunk</setting>
    </event>

    <event path="java/object_alloc_in_new_TLAB">
      <setting name="enabled" control="allocation-profiling-enabled">false</setting>
      <setting name="stackTrace">true</setting>
    </event>

    <event path="java/object_alloc_outside_TLAB">
      <setting name="enabled" control="allocation-profiling-enabled">false</setting>
      <setting name="stackTrace">true</setting>
    </event>

  </producer>

  <producer uri="http://www.oracle.com/hotspot/jdk/" label="Oracle JDK">

    <event path="java/file_read">
      <setting name="enabled">true</setting>
      <setting name="stackTrace">true</setting>
      <setting name="threshold" control="http://www.oracle.com/hotspot/jvm/file-io-threshold">20 ms</setting>
    </event>

    <event path="java/file_write">
      <setting name="enabled">true</setting>
      <setting name="stackTrace">true</setting>
      <setting name="threshold" control="http://www.oracle.com/hotspot/jvm/file-io-threshold">20 ms</setting>
    </event>

    <event path="java/socket_read">
      <setting name="enabled">true</setting>
      <setting name="stackTrace">true</setting>
      <setting name="threshold" control="http://www.oracle.com/hotspot/jvm/socket-io-threshold">20 ms</setting>
    </event>

    <event path="java/socket_write">
      <setting name="enabled">true</setting>
      <setting name="stackTrace">true</setting>
      <setting name="threshold" control="http://www.oracle.com/hotspot/jvm/socket-io-threshold">20 ms</setting>
    </event>

    <event path="java/exception_throw">
      <setting name="enabled" control="http://www.oracle.com/hotspot/jvm/enable-exceptions">false</setting>
      <setting name="stackTrace">true</setting>
    </event>

    <event path="java/error_throw">
      <setting name="enabled" control="http://www.oracle.com/hotspot/jvm/enable-errors">true</setting>
      <setting name="stackTrace">true</setting>
    </event>

    <event path="java/statistics/throwables">
      <setting name="enabled">true</setting>
      <setting name="period">1000 ms</setting>
    </event>

  </producer>

  <producer uri="http://www.oracle.com/hotspot/jfr-info/" label="Oracle JDK">

    <event path="recordings/recording">
      <setting name="enabled">true</setting>
    </event>

    <event path="recordings/recording_setting">
      <setting name="enabled">true</setting>
    </event>

  </producer>

</configuration>
