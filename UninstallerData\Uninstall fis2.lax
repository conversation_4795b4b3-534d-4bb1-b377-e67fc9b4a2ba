#   LaunchAnywhere (tm) Executable Properties File - Flexera Software LLC

#   INCLUDE_DEBUG
#   -------------

INCLUDE_DEBUG=true


#   LAX.APPLICATION.NAME
#   --------------------
#   the default name of this executable -- do not edit

lax.application.name=Uninstall fis2.exe


#   LAX.CLASS.PATH
#   --------------
#   the Java classpath necessary to run this application
#   Can be separated by colons (Mac OS/Unix) or semicolons (Windows)

lax.class.path=;InstData\\Execute.zip;uninstaller.jar;resource;uninstallerResources.zip


#   LAX.COMMAND.LINE.ARGS
#   ---------------------

lax.command.line.args=$CMD_LINE_ARGUMENTS$ -u


#   LAX.DIR
#   -------
#   path to directory holding LaunchAnywhere's native launcher

lax.dir=C:\\Program Files (x86)\\fis2\\UninstallerData\\


#   LAX.MAIN.CLASS
#   --------------
#   the class that contains the main method for the application

lax.main.class=com.zerog.ia.installer.Main


#   LAX.MAIN.METHOD
#   ---------------
#   the method in the main class that will be invoked

lax.main.method=main


#   LAX.NL.CURRENT.VM
#   -----------------
#   the VM to use for the next launch

lax.nl.current.vm=..\\java\\bin\\java.exe


#   LAX.NL.JAVA.COMPILER
#   --------------------

lax.nl.java.compiler=off


#   LAX.NL.JAVA.LAUNCHER.MAIN.CLASS
#   -------------------------------
#   main class of LaunchAnywhere's java launcher -- do not adjust

lax.nl.java.launcher.main.class=com.zerog.lax.LAX


#   LAX.NL.JAVA.LAUNCHER.MAIN.METHOD
#   --------------------------------
#   main method of LaunchAnywhere's java launcher -- do not adjust

lax.nl.java.launcher.main.method=main


#   LAX.NL.JAVA.OPTION.CHECK.SOURCE
#   -------------------------------

lax.nl.java.option.check.source=off


#   LAX.NL.JAVA.OPTION.JAVA.HEAP.SIZE.INITIAL
#   -----------------------------------------

lax.nl.java.option.java.heap.size.initial=16777216


#   LAX.NL.JAVA.OPTION.JAVA.HEAP.SIZE.MAX
#   -------------------------------------

lax.nl.java.option.java.heap.size.max=50331648


#   LAX.NL.MESSAGE.VM.NOT.LOADED
#   ----------------------------
#   text to show the user in a dialog if NO VM can be found.

lax.nl.message.vm.not.loaded=The application either could not find a Java VM, or the Java VM on this system is too old. The application requires Java 1.1.5, Microsoft Java version 2750, or Mac OS Runtime for Java 2.0. These can be downloaded from http://java.sun.com/products/jdk/1.1/jre/ or http://www.microsoft.com/java or http://www.apple.com/java


#   LAX.NL.VALID.VM.LIST
#   --------------------
#   a string containing one or more of JDK JRE MSJ MRJ delimited by spaces or commas.
#   If the native launcher cannot find the current vm,
#   it will search for ones in this list

lax.nl.valid.vm.list=JDK JRE MSJ MRJ


#   LAX.NL.WIN32.JAVA.LAUNCHER.PLATFORM
#   -----------------------------------
#   Set this variable to following values for launching on a 64 bit machine.
#   0, to Prefer 64bit JVM, if not available use 32 bit JVM
#   1, to Prefer 32bit JVM, if not available use 64 bit JVM
#   2, to Prefer 64bit JVM, if not available exit with error
#   3, to Prefer 32bit JVM, if not available exit with error

lax.nl.win32.java.launcher.platform=0


#   LAX.NL.WIN32.MICROSOFTVM.MIN.VERSION
#   ------------------------------------

lax.nl.win32.microsoftvm.min.version=3167


#   LAX.ROOT.INSTALL.DIR
#   --------------------
#   path to the installdir magic folder

lax.root.install.dir=C:\\Program Files (x86)\\fis2


#   LAX.STDERR.REDIRECT
#   -------------------
#   leave blank for no input, "console" to read from the console window,
#   and any path to a file to read from that file

lax.stderr.redirect=


#   LAX.STDIN.REDIRECT
#   ------------------
#   leave blank for no input, "console" to read from the console window,
#   and any path to a file to read from that file

lax.stdin.redirect=


#   LAX.STDOUT.REDIRECT
#   -------------------
#   leave blank for no input, "console" to read from the console window,
#   and any path to a file to read from that file

lax.stdout.redirect=


#   LAX.USER.DIR
#   ------------
#   left blank, this property will cause the native launcher to not
#   alter the platform default behavior for setting the user dir.
#   To override this you may set this property to a relative or absolute path.
#   Relative paths are relative to the launcher.

lax.user.dir=.


#   LAX.VERSION
#   -----------
#   version of LaunchAnywhere that created this properties file

lax.version=17.0


